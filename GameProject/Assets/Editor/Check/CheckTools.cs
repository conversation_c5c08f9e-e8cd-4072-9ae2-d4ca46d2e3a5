using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;

public class CheckTools
{
    [MenuItem("Tools/TA Tools/CheckBoneDuplicateName")]
    public static void CheckBoneDuplicateName()
    {
        //物体内选中
        var selected = Selection.gameObjects;
        foreach (var go in selected)
        {
            if (!PrintChildrenDuplicateNames(go))
            {
                return;
            }
        }
    }
    
    [MenuItem("Assets/TA Tools/CheckBoneDuplicateName")]
    public static void AssetCheckBoneDuplicateName()
    {
        //Project内选中的物体
        var selected = Selection.GetFiltered(typeof(GameObject), SelectionMode.DeepAssets).Cast<GameObject>();
        
        foreach (var go in selected)
        {
            if (!PrintChildrenDuplicateNames(go))
            {
                return;
            }
        }
    }
    
    public static bool PrintChildrenDuplicateNames(GameObject go)
    {
        var names = new List<string>();
        var children = go.GetComponentsInChildren<Transform>();
        var ret = true;
        foreach (var child in children)
        {
            if (names.Contains(child.name))
            {
                //获取child的相对go的路径
                var path = GetRelativePath(child, go.transform);

                Debug.LogError($"重复的骨骼名字:{path}");
                ret = false;
            }
            names.Add(child.name);
        }
        return ret;
    }
    
    public static string GetRelativePath(Transform child, Transform parent)
    {
        var path = child.name;
        while (child.parent != parent)
        {
            child = child.parent;
            path = child.name + "/" + path;
        }

        if (child.parent == parent)
        {
            return path;
        }
        return child.name;//未匹配到parent，返回child的名字
    }
}

using System.Collections.Generic;
using Sirenix.OdinInspector.Editor;
using Sultan.BuildTools;
using UnityEditor;
using UnityEngine;

namespace Sultan.Tools
{
    [EditorToolsAttribute("网格合并")]
    public class MeshCombineTools : IEditorTools
    {
        private static readonly string  DEFAULT_SETTING_PATH = "Assets/CustomSettings/MeshCombine/DefaultCombineSetting.asset";
        private PropertyTree m_Tree;
        private SerializedObject m_SerializedObject;
        public MeshCombineSetting m_CombineSetting;
        //流程管线
        private List<IProcessingPipeline> _processingPipelines;
        
        public void OnEnable()
        {

            if (m_CombineSetting == null)
                m_CombineSetting = AssetDatabase.LoadAssetAtPath<MeshCombineSetting>(DEFAULT_SETTING_PATH);
            _processingPipelines = ProcessingPipelineHelper.InitPipeLineList("CombineTools", m_CombineSetting);
        }

        public void DrawMainGUI()
        {
            EditorGUILayout.BeginHorizontal();
            var oldAsset = m_CombineSetting;
            m_CombineSetting = (MeshCombineSetting)EditorGUILayout.ObjectField("导出配置", m_CombineSetting, typeof(MeshCombineSetting), false);
            if(m_CombineSetting != null && m_CombineSetting != oldAsset)
            {
                m_SerializedObject = new SerializedObject(m_CombineSetting);
                m_Tree = PropertyTree.Create(m_SerializedObject);
                foreach (var pipe in _processingPipelines)
                {
                    pipe.OnEnable(m_CombineSetting);
                }
            }
            else if (m_CombineSetting != null && m_Tree == null)
            {
                m_SerializedObject = new SerializedObject(m_CombineSetting);
                m_Tree = PropertyTree.Create(m_SerializedObject);
            }
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.Space(1);
            if(m_CombineSetting != null)
            {
                m_Tree.BeginDraw(true);
                m_Tree.Draw();
                m_Tree.EndDraw();
                m_SerializedObject?.ApplyModifiedProperties();
            } 
            
            if(_processingPipelines != null)
                ProcessingPipelineHelper.DrawProcessList(ref _processingPipelines);
        }
    }
}
using UnityEngine;
using UnityEditor;
using System.Collections;
using System.Collections.Generic;
using UnityEngine.AI;
using Sultan.Terrain.Runtime;

/// <summary>
/// Author;Aoicocoon
/// Date:20201027
/// 地表导航网格生成工具
/// Unity NavMesh->Mesh->RecastMesh->Bytes
/// </summary>
public sealed class TerrainNavMeshExportEditor : Editor
{
    [UnityEditor.MenuItem("Tools/地形/导航网格")]
    public static void Export2DArray()
    {
        TerrainNavMeshExportEditorWindow window = (TerrainNavMeshExportEditorWindow)EditorWindow.GetWindow<TerrainNavMeshExportEditorWindow>();
        window.minSize = new Vector2(400, 700);
        window.maxSize = window.minSize;
        window.ShowUtility();
    }

    public class TerrainNavMeshExportEditorWindow : EditorWindow
    {
        private int m_sceneExportDataArrSize = 0;
        private TrimAsset[] m_sceneExportDataArr;
        private List<GameObject> m_sceneExportGameObjectList;
        private List<GameObject> m_mapLakeGameObjectList;
        private List<GameObject> m_mapWaterGameObjectList;


        private TerrainConfig m_terrainConfig;//用于还原河道数据




        public void OnGUI()
        {
            m_sceneExportDataArrSize = EditorGUILayout.IntField("装饰物配置数量", m_sceneExportDataArrSize);

            if (null == m_sceneExportDataArr || m_sceneExportDataArr.Length != m_sceneExportDataArrSize)
            {
                TrimAsset[] temp = null;
                if (null != m_sceneExportDataArr)
                {
                    temp = new TrimAsset[m_sceneExportDataArr.Length];
                    System.Array.Copy(m_sceneExportDataArr, temp, temp.Length);
                }
                m_sceneExportDataArr = new TrimAsset[m_sceneExportDataArrSize];

                if (null != temp)
                {
                    int size = Mathf.Min(temp.Length, m_sceneExportDataArrSize);
                    for (int i = 0; i < size; i++)
                    {
                        m_sceneExportDataArr[i] = temp[i];
                    }
                    temp = null;
                }
            }//end if

            for (int i = 0; i < m_sceneExportDataArrSize; i++)
            {
                m_sceneExportDataArr[i] = (TrimAsset)EditorGUILayout.ObjectField(m_sceneExportDataArr[i], typeof(TrimAsset), false);
            }

            EditorGUILayout.LabelField("河道配置数据");
            m_terrainConfig = (TerrainConfig)EditorGUILayout.ObjectField(m_terrainConfig, typeof(TerrainConfig), false);

            if (GUILayout.Button("还原场景装饰物"))
            {
                SetupSceneData();
            }

            if (GUILayout.Button("移除场景装饰"))
            {
                ClearSceneData();
            }

            if (GUILayout.Button("还原场景河道"))
            {
                SetupMapLakeData();
            }

            if (GUILayout.Button("移除场景河道"))
            {
                ClearMapLakeData();
            }

            if (GUILayout.Button("标记所有装饰物及河道为障碍物"))
            {
                MarkSceneObjAsObstacle(true);
                MarkMapLakeAsObstacle(true);
            }

            if (GUILayout.Button("取消标记所有装饰物及河道为障碍物"))
            {
                MarkSceneObjAsObstacle(false);
                MarkMapLakeAsObstacle(false);
            }

            if (GUILayout.Button("隐藏所有标记物"))
            {
                MarkSceneObjVisible(false);
                MarkMapLakeVisible(false);
            }

            if (GUILayout.Button("显示所有标记物"))
            {
                MarkSceneObjVisible(true);
                MarkMapLakeVisible(true);
            }

            if (GUILayout.Button("生成NavMesh"))
            {
                CreateAndBakeNavMesh();
            }

            if (GUILayout.Button("清除NavMesh"))
            {
                CreateAndBakeNavMesh(true);
            }

            if (GUILayout.Button("保存NavMesh->Mesh"))
            {
                SaveNavMesh();
            }
        }

        void SetupSceneData()
        {
            if (null == m_sceneExportDataArr)
            {
                return;
            }
            ClearSceneData();
            m_sceneExportGameObjectList = m_sceneExportGameObjectList ?? new List<GameObject>();
            for (int i = 0; i < m_sceneExportDataArr.Length; i++)
            {
                BuildSceneData(m_sceneExportDataArr[i]);
            }
        }

        void ClearSceneData()
        {
            if (null == m_sceneExportGameObjectList)
            {
                return;
            }

            var data = m_sceneExportGameObjectList.ToArray();
            for (int i = 0; i < data.Length; i++)
            {
                GameObject.DestroyImmediate(data[i]);
            }

            m_sceneExportGameObjectList.Clear();
        }

        void BuildSceneData(TrimAsset data)
        {
            if (null == data)
            {
                return;
            }

            //01 收集场景数据
            Matrix4x4[] MatrixsTrim = data.matrixArr;

            //02还原场景数据
            string pathMeshRes = @"Assets\Res\" + data.meshResPath.Replace(@"/", @"\");
            string pathMaterialRes = @"Assets\Res\" + data.materialResPath.Replace(@"/", @"\");
            Mesh mesh = AssetDatabase.LoadAssetAtPath<Mesh>(pathMeshRes);
            Material mat = AssetDatabase.LoadAssetAtPath<Material>(pathMaterialRes);
            if (mesh && mat)
            {
                for (int i = 0; i < MatrixsTrim.Length; i++)
                {
                    GameObject target = new GameObject(data.meshName);
                    target.AddComponent<MeshFilter>().sharedMesh = mesh;
                    target.AddComponent<MeshRenderer>().sharedMaterial = mat;

                    Matrix4x4 matrix = MatrixsTrim[i];
                    Vector3 position = matrix.GetColumn(3);
                    Quaternion rotation = matrix.rotation;
                    Vector3 scale = matrix.lossyScale;

                    target.transform.localPosition = position;
                    target.transform.rotation = rotation;
                    target.transform.localScale = scale;
                    m_sceneExportGameObjectList.Add(target);
                }
            }           
        }

        void SetupMapLakeData()
        {
            if (null == m_terrainConfig)
            {
                return;
            }

            ClearMapLakeData();

            ChunkData[] chunkDatas = m_terrainConfig.chunkDataArr;

            //01 收集场景河道数据
            Dictionary<Vector3, string> dictPosToPathRiverwayMesh = new Dictionary<Vector3, string>();
            Dictionary<Vector3, string> dictPosToPathWaterbodyMesh = new Dictionary<Vector3, string>();

            for (int i = 0; i < chunkDatas.Length; i++)
            {
                ChunkData chunkData = chunkDatas[i];
                if (chunkData.lake)
                {
                    Vector3 position = chunkData.matrix.GetColumn(3);
                    dictPosToPathRiverwayMesh.Add(position,chunkData.pathRiverwayMesh);
                    dictPosToPathWaterbodyMesh.Add(position,chunkData.pathWaterbodyMesh);
                }
            }

            //02 建立河道
            BuildMapLakeData(dictPosToPathRiverwayMesh, dictPosToPathWaterbodyMesh, m_terrainConfig.pathChunkLakeMaterial,m_terrainConfig.pathChunkWaterbodyMaterial);
        }

        void ClearMapLakeData()
        {
            if (null == m_mapLakeGameObjectList)
            {
                return;
            }

            var data = m_mapLakeGameObjectList.ToArray();
            for (int i = 0; i < data.Length; i++)
            {
                GameObject.DestroyImmediate(data[i]);
            }

            m_mapLakeGameObjectList.Clear();

           /* if (null == m_mapWaterGameObjectList)
            {
                return;
            }
            data = m_mapWaterGameObjectList.ToArray();
            for (int i = 0; i < data.Length; i++)
            {
                GameObject.DestroyImmediate(data[i]);
            }
            m_mapWaterGameObjectList.Clear();*/
        }

        void BuildMapLakeData(Dictionary<Vector3, string> dictPosToPathRiverwayMesh, Dictionary<Vector3, string> dictPosToPathWaterbodyMesh, string pathSharedMatRiverway,string pathSharedMatWaterbody)
        {
            foreach (var ipar in dictPosToPathRiverwayMesh)
            {
                Vector3 position = ipar.Key;
                string pathRiverwayMesh = ipar.Value;
                string pathWaterbodyMesh = dictPosToPathWaterbodyMesh[position];

                pathRiverwayMesh = pathRiverwayMesh.Replace(@"/", @"\");
                pathRiverwayMesh = @"Assets\Res\" + pathRiverwayMesh;
                string pathMatRiverWay = pathSharedMatRiverway.Replace(@"/", @"\");
                pathMatRiverWay = @"Assets\Res\" + pathMatRiverWay;
                pathWaterbodyMesh = pathWaterbodyMesh.Replace(@"/", @"\");
                pathWaterbodyMesh = @"Assets\Res\" + pathWaterbodyMesh;
                string pathMatWater = @"Assets\Res\" + pathSharedMatWaterbody.Replace(@"/", @"\");

                Material matRiverWay = AssetDatabase.LoadAssetAtPath<Material>(pathMatRiverWay);
                Mesh riverWayMesh = AssetDatabase.LoadAssetAtPath<Mesh>(pathRiverwayMesh);
                if (riverWayMesh)
                {
                    GameObject obj = new GameObject("Lake");
                    obj.AddComponent<MeshFilter>().sharedMesh = riverWayMesh;
                    if (matRiverWay)
                    {
                        Material material = new Material(matRiverWay);
                        obj.AddComponent<MeshRenderer>().material = material;
                        obj.transform.position = position;
                        m_mapLakeGameObjectList.Add(obj);
                    }
                }

              /*  //河道
                Material matWater = AssetDatabase.LoadAssetAtPath<Material>(pathMatWater);
                Mesh waterMesh = AssetDatabase.LoadAssetAtPath<Mesh>(pathWaterbodyMesh);
                if (waterMesh)
                {
                    GameObject obj = new GameObject("Water");
                    obj.AddComponent<MeshFilter>().sharedMesh = waterMesh;
                    if (matWater)
                    {
                        Material material = new Material(matWater);
                        obj.AddComponent<MeshRenderer>().material = material;
                        obj.transform.position = position;
                        m_mapWaterGameObjectList.Add(obj);
                    }
                }*/

            }
        }

        void MarkSceneObjAsObstacle(bool isMark)
        {
            if (null == m_sceneExportGameObjectList || 0 == m_sceneExportGameObjectList.Count)
            {
                return;
            }

            for (int i = 0; i < m_sceneExportGameObjectList.Count; i++)
            {
                m_sceneExportGameObjectList[i].layer = isMark ? LayerMask.NameToLayer("Obstacle") : 0;
                if (isMark)
                {
                    GameObjectUtility.SetStaticEditorFlags(m_sceneExportGameObjectList[i], StaticEditorFlags.NavigationStatic);
                }
                else
                {
                    m_sceneExportGameObjectList[i].isStatic = false;
                }
            }
        }

        void MarkMapLakeAsObstacle(bool isMark)
        {
            if (null == m_mapLakeGameObjectList || 0 == m_mapLakeGameObjectList.Count)
            {
                return;
            }
            for (int i = 0; i < m_mapLakeGameObjectList.Count; i++)
            {
                m_mapLakeGameObjectList[i].layer = isMark ? LayerMask.NameToLayer("Obstacle") : 0;
                if (isMark)
                {
                    GameObjectUtility.SetStaticEditorFlags(m_mapLakeGameObjectList[i], StaticEditorFlags.NavigationStatic);
                }
                else
                {
                    m_mapLakeGameObjectList[i].isStatic = false;
                }
            }

            /*
            //河面
            if (null == m_mapWaterGameObjectList || 0 == m_mapWaterGameObjectList.Count)
            {
                return;
            }
            for (int i = 0; i < m_mapWaterGameObjectList.Count; i++)
            {
                m_mapWaterGameObjectList[i].layer = isMark ? LayerMask.NameToLayer("Obstacle") : 0;
                if (isMark)
                {
                    GameObjectUtility.SetStaticEditorFlags(m_mapWaterGameObjectList[i], StaticEditorFlags.NavigationStatic);
                }
                else
                {
                    m_mapWaterGameObjectList[i].isStatic = false;
                }
            }*/
        }

        void MarkSceneObjVisible(bool isVisible)
        {
            if (null == m_sceneExportGameObjectList || 0 == m_sceneExportGameObjectList.Count)
            {
                return;
            }

            for (int i = 0; i < m_sceneExportGameObjectList.Count; i++)
            {
                m_sceneExportGameObjectList[i].SetActive(isVisible);
            }
        }

        void MarkMapLakeVisible(bool isVisible)
        {
            if (null == m_mapLakeGameObjectList || 0 == m_mapLakeGameObjectList.Count)
            {
                return;
            }
            for (int i = 0; i < m_mapLakeGameObjectList.Count; i++)
            {
                m_mapLakeGameObjectList[i].SetActive(isVisible);
            }

            /*
            //河面
            if (null == m_mapWaterGameObjectList || 0 == m_mapWaterGameObjectList.Count)
            {
                return;
            }
            for (int i = 0; i < m_mapWaterGameObjectList.Count; i++)
            {
                m_mapWaterGameObjectList[i].SetActive(isVisible);
            }
            */
        }

        void CreateAndBakeNavMesh(bool clearNavMesh = false)
        {
            GameObject navMeshSurfaceGo = GameObject.Find("TerrainNavMeshSurface");
            if (!navMeshSurfaceGo)
            {
                navMeshSurfaceGo = new GameObject("TerrainNavMeshSurface", typeof(NavMeshSurface));
            }

            NavMeshSurface surface = navMeshSurfaceGo.GetComponent<NavMeshSurface>();

            int navMeshBuildSettingCount = NavMesh.GetSettingsCount();
            for (int i = 0; i < navMeshBuildSettingCount; i++)
            {
                NavMeshBuildSettings buildSetting = NavMesh.GetSettingsByIndex(i);
                string settingName = NavMesh.GetSettingsNameFromID(buildSetting.agentTypeID);
                buildSetting.agentHeight = 0.1f;
                buildSetting.agentRadius = 0.5f;
                buildSetting.agentSlope = 60.0f;
                buildSetting.agentClimb = 0.4f;

                Debug.Log(string.Format("Agent Type Name: {0}, Radius: {1}, Height: {2}", settingName, buildSetting.agentRadius, buildSetting.agentHeight));
            }
            surface.layerMask = LayerMask.GetMask(new string[] { "Obstacle", "TerrainSurface" });

            UnityEditor.AI.NavMeshAssetManager.instance.ClearSurfaces(new Object[] { surface });

            if (!clearNavMesh)
            {
                UnityEditor.AI.NavMeshAssetManager.instance.StartBakingSurfaces(new Object[] { surface });
            }

            Selection.activeGameObject = navMeshSurfaceGo;
        }

        void SaveNavMesh()
        {
            GameObject navMeshSurfaceGo = GameObject.Find("TerrainNavMeshSurface");
            if (!navMeshSurfaceGo)
            {
                return;
            }

            MeshFilter mf = navMeshSurfaceGo.GetComponent<MeshFilter>();
            if (!mf)
            {
                mf = navMeshSurfaceGo.AddComponent<MeshFilter>();
            }

            if (mf.sharedMesh)
            {
                GameObject.DestroyImmediate(mf.sharedMesh, true);
                mf.sharedMesh = null;
            }

            MeshRenderer mr = navMeshSurfaceGo.GetComponent<MeshRenderer>();
            if (!mr)
            {
                mr = navMeshSurfaceGo.AddComponent<MeshRenderer>();
            }

            NavMeshTriangulation triangulation = NavMesh.CalculateTriangulation();
            Mesh m = new Mesh();
            m.vertices = triangulation.vertices;
            m.triangles = triangulation.indices;

            mf.sharedMesh = m;
        }

        //end func

    }


}
using System.IO;
using UnityEditor;
using UnityEngine;
using Sultan.Code;

public class UnUseMoveTools
{
    [MenuItem("Assets/资源/标识无用资源")]
    public static void MoveToUnUsed()
    {
        var objects = UnityEditor.Selection.objects;
        foreach (var obj in objects)
        {
            var assetPath = AssetDatabase.GetAssetPath(obj);
            assetPath.Replace("\\", "/");
            
            if (!assetPath.StartsWith("Assets/UnUsed"))
            {
                var replacePath = assetPath.Replace("ArtTmp/", "UnUsed/");
                replacePath = replacePath.Replace("Res/", "UnUsed/");
                if (replacePath == assetPath)
                    continue;

                var folderPath = Path.GetDirectoryName(replacePath);

                if (!AssetDatabase.IsValidFolder(folderPath))
                {
                    // var rootFolder = Path.GetDirectoryName(folderPath);;
                    // var name = Path.GetFileName(folderPath);
                    // Debug.Log($"{rootFolder} {name}");
                    // // var folderPath = Sultan.Core.FileUtil.DirName("1111");
                    //
                    // AssetDatabase.CreateFolder(rootFolder, name);
                    Directory.CreateDirectory(folderPath);
                    AssetDatabase.Refresh();
                }
                    
                Debug.Log($"{assetPath}, {replacePath} ");

                AssetDatabase.MoveAsset(assetPath, replacePath);
            }
        }
    }
    
    [MenuItem("Assets/资源/标识有用资源")]
    public static void MoveUnUsedBack()
    {
        var objects = UnityEditor.Selection.objects;
        foreach (var obj in objects)
        {
            var assetPath = AssetDatabase.GetAssetPath(obj);
            assetPath.Replace("\\", "/");
            
            if (assetPath.StartsWith("Assets/UnUsed"))
            {
                var replacePath = assetPath.Replace("UnUsed/", "ArtTmp/");
                // replacePath = replacePath.Replace("Res/", "UnUsed/");
                if (replacePath == assetPath)
                    continue;

                var folderPath = Path.GetDirectoryName(replacePath);

                if (!AssetDatabase.IsValidFolder(folderPath))
                {
                    var rootFolder = Path.GetDirectoryName(folderPath);;
                    var name = Path.GetFileName(folderPath);
                    Debug.Log($"{rootFolder} {name}");
                    // var folderPath = Sultan.Core.FileUtil.DirName("1111");

                    AssetDatabase.CreateFolder(rootFolder, name);
                }
                    
                Debug.Log($"{assetPath}, {replacePath} ");

                AssetDatabase.MoveAsset(assetPath, replacePath);
            }
        }
    }
    
}
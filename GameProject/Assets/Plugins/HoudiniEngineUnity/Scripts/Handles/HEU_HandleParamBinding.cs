/*
* Copyright (c) <2020> Side Effects Software Inc.
* All rights reserved.
*
* Redistribution and use in source and binary forms, with or without
* modification, are permitted provided that the following conditions are met:
*
* 1. Redistributions of source code must retain the above copyright notice,
*    this list of conditions and the following disclaimer.
*
* 2. The name of Side Effects Software may not be used to endorse or
*    promote products derived from this software without specific prior
*    written permission.
*
* THIS SOFTWARE IS PROVIDED BY SIDE EFFECTS SOFTWARE "AS IS" AND ANY EXPRESS
* OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.  IN
* NO EVENT SHALL SIDE EFFECTS SOFTWARE BE LIABLE FOR ANY DIRECT, INDIRECT,
* INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
* LIMITED TO, PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA,
* OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
* LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
* NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
* EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/

using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace HoudiniEngineUnity
{
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // Typedefs (copy these from HEU_Common.cs)
    using HAPI_NodeId = System.Int32;
    using HAPI_ParmId = System.Int32;

    /// <summary>
    /// Represents a Handle binding to a parameter.
    /// </summary>
    [System.Serializable]
    internal class HEU_HandleParamBinding : IEquivable<HEU_HandleParamBinding>
    {
	public enum HEU_HandleParamType
	{
	    TRANSLATE,
	    ROTATE,
	    SCALE
	}

	public HEU_HandleParamType _paramType;

	public HAPI_ParmId _parmID;

	public string _paramName;

	public bool _bDisabled;

	public bool[] _boundChannels = new bool[3];

	public bool IsEquivalentTo(HEU_HandleParamBinding other)
	{

	    bool bResult = true;

	    string header = "HEU_HandleParamBinding";

	    if (other == null)
	    {
		HEU_Logger.LogError(header + " Not equivalent");
		return false;
	    }

	    HEU_TestHelpers.AssertTrueLogEquivalent(this._paramType, other._paramType, ref bResult, header, "_paramType");

	    // SKip parmID

	    HEU_TestHelpers.AssertTrueLogEquivalent(this._paramName, other._paramName, ref bResult, header, "_paramName");
	    HEU_TestHelpers.AssertTrueLogEquivalent(this._bDisabled, other._bDisabled, ref bResult, header, "_bDisabled");
	    HEU_TestHelpers.AssertTrueLogEquivalent(this._boundChannels, other._boundChannels, ref bResult, header, "_boundChannels");

	    return bResult;
	}
    }

}   // HoudiniEngineUnity
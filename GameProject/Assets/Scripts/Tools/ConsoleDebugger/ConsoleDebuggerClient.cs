using System;
using UnityEngine;
using System.Net.Sockets;
using System.Text;

public class ConsoleDebuggerClient
{

    public static ConsoleDebuggerClient Instance
    {
        get; set;
    }

    public static ConsoleDebuggerClient GetInstance()
    {
        if (Instance == null)
        {
            Instance = new ConsoleDebuggerClient();
        }

        return Instance;
    }

    private static NetworkStream sendStream;
    private static TcpClient client;
    private byte[] buf = new byte[1024 * 1024];


    public bool Connect(string host, int port)
    {
#if UNITY_EDITOR
        try
        {
            client = new TcpClient();
            client.SendTimeout = 3000;
            client.NoDelay = true;
            client.SendBufferSize = buf.Length;
            client.ReceiveBufferSize = buf.Length;
            client.Connect(host, port);
            if (client.Connected)
            {
                sendStream = client.GetStream();
                return true;
            }
        }
        catch (Exception e)
        {
            Debug.Log("Socket error: " + e);
        }
        return false;
#else
    return false;
#endif
    }

    public void Send(string msg)
    {
#if UNITY_EDITOR
        try
        {
            if (!IsConnected())
                return;
            byte[] sendBytes = Encoding.Default.GetBytes(msg);
            int length = sendBytes.Length;
            sendStream.Write(sendBytes, 0, length);
        }
        catch (Exception e)
        {
            Debug.Log(e);
        }
#endif
    }

    public bool IsConnected()
    {
        return client != null && client.Connected;
    }


    public void Close()
    {
#if UNITY_EDITOR
        if (sendStream != null)
        {
            try
            {
                sendStream.Flush();
                sendStream.Close();
                sendStream = null;
            }
            catch (System.Exception e)
            {
                Debug.LogError(e.ToString());
            }
        }

        if (client != null)
        {
            try
            {
                client.Close();
                client.Dispose();
                client = null;
                Debug.Log("close tcpClient connect.");
            }
            catch (System.Exception e)
            {

                Debug.LogError(e.ToString());
            }
        }
#endif
    }
}

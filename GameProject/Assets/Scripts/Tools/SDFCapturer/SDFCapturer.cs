//file: SDFCapturer.cs
//Author: Aoicocoon
//Date: 2023-09-26 11:14

using System;
using System.Collections.Generic;

#if UNITY_EDITOR
using System.Net;
using UnityEditor;
using UnityEngine.Rendering;
#endif
using UnityEngine;
using UnityEngine.SceneManagement;

namespace UnityTemplateProjects.Tools.SDFCapturer
{
    public class SDFCapturer : MonoBehaviour
    {
#if UNITY_EDITOR
        [Header("导出为png格式")]
        public bool ExportPngFile = false;
        [Header("跳过Transparent")]
        public bool SkipTransparentMaterial = false;
        [Header("跳过AlphaTest")]
        public bool SkipAlphaTest = false;
        [Header("1.选择距离捕获模型")]
        public GameObject CaptureTarget;
        [Header("2.选择距离捕获模型上的网格碰撞体")]
        public Collider CaptureCollider;
        [Header("3.生成的SDF纹理尺寸")]
        public Vector2 TextureSize = Vector2.zero;
        [Header("4.在捕获模型平面上发射的射线距离")]
        public float RayCastingDistance = 10.0f;
        [Header("5.点我")]
        public bool StartCapture;
        public Texture2D OutputTex;
        
        private Texture2D m_outputTex;

        void OnValidate()
        {
            if (StartCapture)
            {
                StartCapture = false;
                bool ret = Prepare();
                if(ret)
                {  
                    BuildScene();
                    BuildTexture();
                    RayCasting();
                    ClearScene();
                    Output();
                }
            }
        }

        bool Prepare()
        {
            if (!CaptureTarget)
            {
                EditorUtility.DisplayDialog("Error", "Please choose a Target", "OK");
                return false;
            }

            if (!CaptureCollider)
            {
                EditorUtility.DisplayDialog("Error", "Please choose a TargetCollider", "OK");
                return false;
            }

            if (TextureSize == Vector2.zero)
            {
                EditorUtility.DisplayDialog("Error", "Please set  TextureSize", "OK");
                return false;
            }

            return true;
        }

        void RayCasting()
        {
            if (m_samplerPerPixelH == 0 || m_samplerPerPixelW == 0)
            {
                return;
            }

            CaptureCollider.enabled = false;
            Renderer targetRender = CaptureTarget.GetComponent<Renderer>();
            Bounds bound = targetRender.bounds;

            float startX = bound.min.x;
            float startZ = bound.min.z;
            float endX = bound.max.x;
            float endZ = bound.max.z;
            float centerY = bound.min.y + (bound.max.y - bound.min.y) * 0.5f;

            Color[] outputCols = m_outputTex.GetPixels();
            for (int i = 0; i < m_outputTex.height; i ++)
            {
                for (int j = 0; j < m_outputTex.width; j ++)
                {
                    Vector3 markPos = new Vector3(j * m_samplerPerPixelW, centerY, i * m_samplerPerPixelH);
                    markPos.x += startX;
                    markPos.z += startZ;
                    
                    if (markPos.x >= endX)
                    {
                        markPos.x = endX;
                    }

                    if (markPos.z >= endZ)
                    {
                        markPos.z = endZ;
                    }
                    
                   
                    float d = GenerateRay(markPos, RayCastingDistance);

                    int colIdx = i * m_outputTex.width + j;
                    int invIdx = (outputCols.Length - 1) - colIdx;
                    outputCols[invIdx].r = d;

                    // GameObject testNode = new GameObject();
                    // testNode.name = i + "_" + j + "_" + d;
                    // testNode.transform.position = markPos;
                    // testNode.transform.localScale = new Vector3(m_samplerPerPixelW * 0.5f, 1.0f, m_samplerPerPixelH * 0.5f);
                    // testNode.transform.parent = this.transform;
                    
                }
            }
            
            m_outputTex.SetPixels(outputCols);
            m_outputTex.Apply(false);

            CaptureCollider.enabled = true;
        }

        float GenerateRay(Vector3 rayOriginPos, float RayCastingDistance)
        {
            //正向求交 存最近表面距离
            float minDis = RayCastingDistance;
            int sign = 1;
            int rayRow = 32;
            for (int i = 0; i < rayRow; i++)
            {
                for (int j = 0; j <= rayRow; j++)
                {
                    Vector3 dir = Vector3.zero;
                    float yLen = Mathf.Sin(Mathf.Deg2Rad * (j - rayRow/2) * 180 / rayRow);
                    float xzLen = Mathf.Sqrt(1 - yLen * yLen);
                    dir.y = yLen;
                    dir.x = Mathf.Cos(Mathf.Deg2Rad * i * 360 / rayRow) * xzLen;
                    dir.z = Mathf.Sin(Mathf.Deg2Rad * i * 360 / rayRow) * xzLen;
			 
                    RaycastHit hitInfo;

                    if (Physics.Raycast(rayOriginPos, dir, out hitInfo, RayCastingDistance))
                    {
                        if (minDis > hitInfo.distance)
                        {
                            minDis = hitInfo.distance;
                        }
                    }

                }
            }

            //反向求交 存最近表面距离
            for (int i = 0; i < rayRow; i++)
            {
                for (int j = 0; j <= rayRow; j++)
                {
                    Vector3 dir = Vector3.zero;
                    float yLen = Mathf.Sin(Mathf.Deg2Rad * (j - rayRow/2) * 180 / rayRow);
                    float xzLen = Mathf.Sqrt(1 - yLen * yLen);
                    dir.y = yLen;
                    dir.x = Mathf.Cos(Mathf.Deg2Rad * i * 360 / rayRow) * xzLen;
                    dir.z = Mathf.Sin(Mathf.Deg2Rad * i * 360 / rayRow) * xzLen;

                    RaycastHit[] hitInfos = Physics.RaycastAll(rayOriginPos + dir * minDis, -dir, minDis);
                    float maxRevertDis = 0;
                    foreach (var hitInfo in hitInfos)
                    {
                        maxRevertDis = Mathf.Max(maxRevertDis, hitInfo.distance);
                        sign = -1;
                    }
                    if (maxRevertDis != 0) {
                        minDis = minDis - maxRevertDis;
                    }
                }
            }
            
            

            minDis *= sign;

            return minDis;
        }

        void Output()
        {
            Scene curScene = SceneManager.GetActiveScene();
            string outputName = null != curScene ? curScene.name : "NoName";
            DateTime nowTime = DateTime.Now;
            string timeStr = nowTime.ToString("yyyy_M_d_HH_mm_ss");
            
            Renderer targetRender = CaptureTarget.GetComponent<Renderer>();
            Bounds bound = targetRender.bounds;

            string boundsStr = string.Format("x_{0}_y_{1}_z{2}-x{0}_y{1}_z{2}", bound.min.x, bound.min.y, bound.min.z,
                bound.max.x, bound.max.y, bound.max.z);
                
            outputName = "Scene_" + outputName + "_" + timeStr + "_" + CaptureTarget.name + "_" + boundsStr + "_SDF";

            string saveFolder = "Scripts/Tools/SDFCapturer/Output/";
            string savePath =  "Assets/" + saveFolder+ outputName + ".asset";
            AssetDatabase.CreateAsset(m_outputTex, savePath);

            if (ExportPngFile)
            {
                byte[] pngFile = m_outputTex.EncodeToPNG();
                System.IO.File.WriteAllBytes(System.IO.Path.Combine(Application.dataPath,saveFolder) +outputName + ".png", pngFile);
            }

            AssetDatabase.Refresh();
            
            Texture2D savedAsset =
                AssetDatabase.LoadAssetAtPath<Texture2D>(savePath);

            OutputTex = savedAsset;

            MeshRenderer mr = CaptureTarget.GetComponent<MeshRenderer>();
            if (mr)
            {
                if (mr.sharedMaterial.shader.name == "Unlit/SDFTestShader")
                {
                    mr.sharedMaterial.SetFloat("_Height", RayCastingDistance * 0.5f);
                    mr.sharedMaterial.SetTexture("_MainTex", OutputTex);
                }
            }
        }

        void ClearScene()
        {
            if (null != m_fullSceneRenderersObj)
            {
                foreach (MeshCollider obj in m_fullSceneRenderersObj)
                {
                    if (!obj) continue;

                    GameObject.DestroyImmediate(obj);
                }

                m_fullSceneRenderersObj.Clear();
                m_fullSceneRenderersObj = null;
            }
        }

        private List<MeshCollider> m_fullSceneRenderersObj;
        void BuildScene()
        {
            MeshFilter[] renders = FindObjectsByType<MeshFilter>(FindObjectsSortMode.None);

            if (null == renders)
            {
                return;
            }

            if (null == m_fullSceneRenderersObj)
            {
                m_fullSceneRenderersObj = new List<MeshCollider>();
            }
            m_fullSceneRenderersObj.Clear();

            for (int i = 0; i < renders.Length; i++)
            {
                GameObject go = renders[i].gameObject;

                if (go == CaptureTarget)
                {
                    continue;
                }
                
                MeshFilter mf = renders[i];
                MeshRenderer mr = mf.GetComponent<MeshRenderer>();

                if (mr && mr.sharedMaterial)
                {
                    if (SkipTransparentMaterial)
                    {
                        if (mr.sharedMaterial.renderQueue >= (int)RenderQueue.Transparent &&
                            mr.sharedMaterial.renderQueue < (int)RenderQueue.Overlay)
                        {
                            continue;
                        }
                    }

                    if (SkipAlphaTest)
                    {
                        if (mr.sharedMaterial.renderQueue >= (int)RenderQueue.AlphaTest &&
                            mr.sharedMaterial.renderQueue < (int)RenderQueue.GeometryLast)
                        {
                            continue;
                        }
                    }
                }

                MeshCollider collider = null;
                go.TryGetComponent<MeshCollider>(out collider);

                if (!collider)
                {
                    collider = go.AddComponent<MeshCollider>();
                    m_fullSceneRenderersObj.Add(collider);
                }
                
                collider.sharedMesh = mf.sharedMesh;
            }
            
            SkinnedMeshRenderer[] smrs = FindObjectsByType<SkinnedMeshRenderer>(FindObjectsSortMode.None);
            for (int i = 0; i < smrs.Length; i++)
            {
                GameObject go = smrs[i].gameObject;

                if (go == CaptureTarget)
                {
                    continue;
                }
                
                SkinnedMeshRenderer smr = smrs[i];

                if (smr && smr.sharedMaterial)
                {
                    if (SkipTransparentMaterial)
                    {
                        if (smr.sharedMaterial.renderQueue >= (int)RenderQueue.Transparent &&
                            smr.sharedMaterial.renderQueue < (int)RenderQueue.Overlay)
                        {
                            continue;
                        }
                    }

                    if (SkipAlphaTest)
                    {
                        if (smr.sharedMaterial.renderQueue >= (int)RenderQueue.AlphaTest &&
                            smr.sharedMaterial.renderQueue < (int)RenderQueue.GeometryLast)
                        {
                            continue;
                        }
                    }
                }
                
                MeshCollider collider = null;
                go.TryGetComponent<MeshCollider>(out collider);

                if (!collider)
                {
                    collider = go.AddComponent<MeshCollider>();
                    m_fullSceneRenderersObj.Add(collider);
                }

                collider.sharedMesh = smr.sharedMesh;
            }
        }

        float m_samplerPerPixelW = 0;
        float m_samplerPerPixelH = 0;
        
        void BuildTexture()
        {
            Renderer targetRender = CaptureTarget.GetComponent<Renderer>();
            Bounds bound = targetRender.bounds;
            float aspect = bound.extents.x / bound.extents.z;
           
            m_outputTex = new Texture2D((int)TextureSize.x, (int)TextureSize.y, TextureFormat.R8, false);

            m_samplerPerPixelW = bound.size.x / TextureSize.x;
            m_samplerPerPixelH = bound.size.z / TextureSize.y;
            
            Debug.LogFormat("World W {0} L {1} aspect {2} sw {3} sh {4}", bound.size.x, bound.size.z, aspect, m_samplerPerPixelW, m_samplerPerPixelH);
        }
#endif
    }
}
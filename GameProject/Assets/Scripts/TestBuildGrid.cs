using System.Collections;
using System.Collections.Generic;
using UnityEngine;


[ExecuteAlways]
public class TestBuildGrid : MonoBehaviour
{
    public Material matBuildGrid;
    public List<float> gridMask = new List<float>();
    // Start is called before the first frame update
    void Start()
    {
        
    }

    private void OnValidate()
    {
        if (matBuildGrid != null){
            matBuildGrid.SetFloatArray("_GridMask", gridMask.ToArray());
        }
    }

    // Update is called once per frame
    void Update()
    {
        if (matBuildGrid != null){
            matBuildGrid.SetFloatArray("_GridMask", gridMask.ToArray());
        }
    }
}

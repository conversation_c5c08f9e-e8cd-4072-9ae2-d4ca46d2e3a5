
using System.Collections.Generic;
using System.Text;
using Sultan.Core;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;

//Author:Aoicocoon
//Date:20231129
class MaterialRVTSwitcher : EditorWindow
{
    private bool SetRvtOn = false;

    [MenuItem ("Tools/地形/地表物件RVT切换")]
    static void Main()
    {
        var d = EditorWindow.GetWindow(typeof(MaterialRVTSwitcher));
        d.position = new Rect(Screen.resolutions[0].width / 2.0f, Screen.resolutions[0].height / 2.0f, 200, 100);
    }

    void OnGUI ()
    {
        
        GUILayout.Label ("开启RVT", EditorStyles.boldLabel);
        bool ret = EditorGUILayout.Toggle("SetRVT", SetRvtOn);

        if (ret != SetRvtOn)
        {
            SetRvtOn = ret;
            DoRVT(SetRvtOn);
        }
    }

    void DoRVT(bool on)
    {
        List<string> modifyMat = new List<string>();
        var guids = AssetDatabase.FindAssets("t:Material", new []{"Assets"});
        foreach (var gid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(gid);
            Material mat = AssetDatabase.LoadAssetAtPath<Material>(path);
            if (mat)
            {
               
                bool findKeyword = false;
                var keyworlds = mat.shader.keywordSpace;
                foreach (var k in keyworlds.keywordNames)
                {
                    if (k == "RVT_ON")
                    {
                        findKeyword = true;
                        break;
                    }
                }

                if (findKeyword)
                {
                    modifyMat.Add(path);
                    if (on)
                    {
                        
                        mat.SetInt("RVT_ON", 1);
                        mat.EnableKeyword("RVT_ON");
                        mat.SetInt("_RenderType", 0);//1 opaque
                        mat.renderQueue = (int)RenderQueue.Geometry;
                    }
                    else
                    {
                        mat.SetInt("RVT_ON", 0);
                        mat.DisableKeyword("RVT_ON");
                        mat.SetInt("_RenderType", 1);//1 transparent
                        mat.renderQueue = (int)RenderQueue.Transparent;
                    }
                }
                
            }
           
        }
        AssetDatabase.Refresh();
        StringBuilder sb = new StringBuilder();
        int limit = 20;
        foreach (var s in modifyMat)
        {
            if (limit-- > 0)
            {
                sb.AppendLine(s);
            }
            
        }
        Debug.Log(sb.ToString());
        EditorUtility.DisplayDialog("材质被修改，记得手动提交git(只显示部分)", sb.ToString(), "ok");
    }
}

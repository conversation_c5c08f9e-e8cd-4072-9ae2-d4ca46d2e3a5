using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

public class SetCharacterLayerAndLightMask : EditorWindow
{
    [MenuItem("Assets/TA Tools/Set Character Layer and Light Layer", false, -1)]
    static void SetLayerAndLightMaskForPrefabs()
    {
        List<GameObject> prefabsToProcess = new List<GameObject>();

        foreach (Object obj in Selection.GetFiltered(typeof(Object), SelectionMode.Assets))
        {
            if (obj is GameObject go && PrefabUtility.GetPrefabAssetType(go) != PrefabAssetType.NotAPrefab)
            {
                prefabsToProcess.Add(go);
            }
        }

        if (prefabsToProcess.Count == 0)
        {
            Debug.LogWarning("No valid prefabs selected.");
            return;
        }

        ProcessPrefabs(prefabsToProcess.ToArray());
    }

    private static void ProcessPrefabs(GameObject[] prefabs)
    {
        int characterLayer = LayerMask.NameToLayer("Character");
        if (characterLayer == -1)
        {
            Debug.LogError("Layer 'Character' does not exist! Please create it first.");
            return;
        }

        int successCount = 0;
        int failCount = 0;
        
        try
        {
            for (int i = 0; i < prefabs.Length; i++)
            {
                GameObject selectedPrefab = prefabs[i];
                string prefabName = selectedPrefab.name;
                string prefabPath = AssetDatabase.GetAssetPath(selectedPrefab);

                // 显示进度条
                if (EditorUtility.DisplayCancelableProgressBar(
                    "Processing Prefabs",
                    $"({i + 1}/{prefabs.Length}) {prefabName}",
                    (float)i / prefabs.Length))
                {
                    Debug.LogWarning("Operation canceled by user");
                    break;
                }

                if (string.IsNullOrEmpty(prefabPath) || !prefabPath.EndsWith(".prefab"))
                {
                    Debug.LogError($"Invalid prefab: {prefabName}");
                    failCount++;
                    continue;
                }

                GameObject rootGO = PrefabUtility.LoadPrefabContents(prefabPath);
                if (rootGO == null)
                {
                    Debug.LogError($"Failed to load prefab: {prefabName}");
                    failCount++;
                    continue;
                }

                bool modified = false;
                foreach (Transform trans in rootGO.GetComponentsInChildren<Transform>(true))
                {
                    // 设置层级
                    if (trans.gameObject.layer != characterLayer)
                    {
                        trans.gameObject.layer = characterLayer;
                        modified = true;
                    }

                    // 处理所有渲染器类型
                    Renderer renderer = trans.GetComponent<Renderer>();
                    if (renderer != null)
                    {
                        // 只设置需要修改的渲染器
                        if (renderer.renderingLayerMask != (1 << 1))
                        {
                            renderer.renderingLayerMask = 1 << 1; // Light Layer 1
                            modified = true;
                        }
                    }
                }

                if (modified)
                {
                    try
                    {
                        PrefabUtility.SaveAsPrefabAsset(rootGO, prefabPath);
                        successCount++;
                        Debug.Log($"Successfully updated: {prefabName}", selectedPrefab);
                    }
                    catch (System.Exception ex)
                    {
                        failCount++;
                        Debug.LogError($"Failed to save {prefabName}: {ex.Message}");
                    }
                }
                else
                {
                    Debug.Log($"No changes needed: {prefabName}", selectedPrefab);
                }

                PrefabUtility.UnloadPrefabContents(rootGO);
            }
        }
        finally
        {
            EditorUtility.ClearProgressBar();
            Debug.Log($"Operation completed. Successful: {successCount}, Failed: {failCount}, Total: {prefabs.Length}");
        }
    }
}
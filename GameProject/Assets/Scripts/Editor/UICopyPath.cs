using System.Collections;
using System.Collections.Generic;
using EditorFramework;
using FairyGUI;
using Sultan.Core;
using UnityEditor;
using UnityEngine;

public static class UICopyPath 
{
    [UnityEditor.MenuItem("GameObject/FairyGUI/CopyPath", true, 100)]
    public static bool CopyUIPathEnabled()
    {
        if (Selection.activeGameObject == null)
            return false;
        
        var displayObject = Selection.activeGameObject.GetComponent<DisplayObjectInfo>();
        if (displayObject == null)
            return false;
        return true;
    }
    
    [UnityEditor.MenuItem("GameObject/FairyGUI/CopyPath")]
    public static void CopyUIPath()
    {
        if (Selection.activeGameObject == null)
            return;
        
        var displayObjectInfo = Selection.activeGameObject.GetComponent<DisplayObjectInfo>();
        if (displayObjectInfo == null)
            return;

        var name = string.Empty;
        if (displayObjectInfo.displayObject.gOwner != null)
            name = displayObjectInfo.displayObject.gOwner.name;
        var p = displayObjectInfo.displayObject.gOwner.parent;
        while (p != null)
        {
            if (p.name.Equals("view"))
            {
                name = "view/" + name;
                break;
            }
            else
            {
                name = p.name + "/" + name;
                p = p.parent;
            }
        }
        ClipboardUtil.Copy(name);
        Debug.Log("uipath:" + name);
    }
}

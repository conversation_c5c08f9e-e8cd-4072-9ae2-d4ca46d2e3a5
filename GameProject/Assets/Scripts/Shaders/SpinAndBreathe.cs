using System;
using UnityEngine;

namespace Sultan.Test
{
    //[ExecuteAlways]
    [DisallowMultipleComponent]
    public class SpinAndBreathe : MonoBehaviour
    {
        // 自旋速度
        public float RotationSpeed = 100.0f;
        public bool ColockWise = true;

        // 呼吸运动的速度
        public float BreatheSpeed = 2.0f;

        // 呼吸运动的最大位移
        public float BreatheAmount = 0.5f;

        // 初始位置
        private Vector3 originalPosition;

        private void OnValidate()
        {
            originalPosition = transform.localPosition;
        }

        void Start()
        {
            originalPosition = transform.localPosition;
        }

        void Update()
        {
            
            float angle = RotationSpeed * Time.deltaTime;
            float dir =  ColockWise ? 1.0f : -1.0f;
            angle = angle * dir;
            if (Mathf.Abs(angle) > 360.0f)
            {
                angle = 0.0f;
            }

            transform.Rotate(Vector3.up, angle);

            
            float breatheOffset = Mathf.Sin(Time.time * BreatheSpeed) * BreatheAmount;
            transform.localPosition = originalPosition + new Vector3(0, breatheOffset, 0);
        }
    }
}

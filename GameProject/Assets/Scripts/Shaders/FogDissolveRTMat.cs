using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Sultan.Test
{
    [DisallowMultipleComponent]
    public class FogDissolveRTMat : MonoBehaviour
    {
        [Header("是否解锁")] public bool UnlockArea;
        [Header("解锁速度")] public float UnlockSpeed = 0.15f;

        private static readonly int fogRTColorID = Shader.PropertyToID("_BaseColor");

        private Renderer _renderer;

        private MaterialPropertyBlock _materialPropertyBlock;

        private float _timeInterpolator = 0.0f;
        private float _timePercent = 0.0f;

        private Color color = Color.white;

        void Awake()
        {
            _renderer = GetComponentInChildren<Renderer>();
        }

        // Update is called once per frame
        void Update()
        {
            if (_renderer !=null && UnlockArea)
            {
                _timeInterpolator += Time.deltaTime * UnlockSpeed;
                _timePercent = Mathf.Min(_timeInterpolator, 1.0f);

                color *= 1.0f - _timePercent;
                if (_materialPropertyBlock == null)
                {
                    _materialPropertyBlock = new MaterialPropertyBlock();
                }

                _materialPropertyBlock.SetColor(fogRTColorID, color);

                _renderer.SetPropertyBlock(_materialPropertyBlock);

                if (_timeInterpolator > 1.0f)
                {
                    UnlockArea = !UnlockArea;
                }
            }
        }
    }
}
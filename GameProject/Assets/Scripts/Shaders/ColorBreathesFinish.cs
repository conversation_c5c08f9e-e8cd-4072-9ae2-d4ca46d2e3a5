using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Sultan.Test
{
    [DisallowMultipleComponent]
    [ExecuteAlways]
    public class ColorBreathesFinish : MonoBehaviour
    {
        [Header("呼吸的最大亮度")]
        public float MaxIntensity = 0.45f;
        [Header("呼吸的速度")]
        public float BreathSpeed = 4f;

        private Renderer[] renderers;
        private MaterialPropertyBlock block;
        private static readonly int AddPropertyID = Shader.PropertyToID("_AddColor");

        private float timeIterator = 0f;


        void OnEnable()
        {
            renderers = GetComponentsInChildren<Renderer>();
            block = new MaterialPropertyBlock();
        }

        void OnDisable()
        {
            ClearDatas();
        }

        void OnDestroy()
        {
            ClearDatas();
        }

        // Update is called once per frame
        void Update()
        {
            if (renderers!= null &&renderers.Length > 0)
            {
                var temp = SinTimeFloat(MaxIntensity, BreathSpeed);
                ChangeAddColor(temp);
            }
        }

        void ClearDatas()
        {
            if (renderers!= null &&renderers.Length > 0)
            {
                ChangeAddColor(0f);
            }

            renderers = null;
            block = null;
            timeIterator = 0f;
        }

        float SinTimeFloat(float intensity, float speed)
        {
            float sinOut;
            timeIterator += Time.deltaTime * speed;

            sinOut = Mathf.Sin(timeIterator) * 0.5f + 0.5f;
            sinOut *= intensity;
            return sinOut;
        }

        void ChangeAddColor(float intensity)
        {
            // 将颜色值乘以强度参数
            Color newColor = new Color(1, 1, 1, 1) * intensity;
            foreach (Renderer renderer in renderers)
            {
                // 检查是否含有材质及参数
                if (renderer.sharedMaterial != null && renderer.sharedMaterial.HasProperty(AddPropertyID))
                {
                    // 设置新的_AddColor值
                    block.SetColor(AddPropertyID, newColor);
                    renderer.SetPropertyBlock(block);
                }
            }
        }
    }
}
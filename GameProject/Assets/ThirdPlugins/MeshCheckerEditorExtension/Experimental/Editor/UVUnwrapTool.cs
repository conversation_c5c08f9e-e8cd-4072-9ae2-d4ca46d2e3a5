using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.MeshCheckerEditorExtension.UserInterface;
using HightlanderSolutions.MeshCheckerEditorExtension.Utilities;
using UnityEditor;
using UnityEngine;

[MeshAnalysisTool("UV Unwrap", 211)]
public sealed class UVUnwrapTool : MeshAnalysisTool
{
    private Vector3[] Vertices;
    private int[] Indices;
    private Vector2[] UVs;
    private Mesh Target;

    private float T;
    private Texture2D Texture;

    public override void Initialize(Mesh mesh)
    {
        Target = mesh;
        Indices = new int[mesh.triangles.Length];
        mesh.triangles.CopyTo(Indices, 0);
        Vertices = new Vector3[mesh.vertices.Length];
        mesh.vertices.CopyTo(Vertices, 0);
        UVs = new Vector2[mesh.uv.Length];
        mesh.uv.CopyTo(UVs, 0);
    }

    public override void Deinitialize()
    {
        Target = null;
        Indices = null;
        Vertices = null;
        UVs = null;
    }

    public override void DrawToolbar()
    {
        T = GUILayout.HorizontalSlider(T, 0f, 1f, GUILayout.Width(400));
        
        GUILayout.Label("Reference");
        Texture = EditorGUILayout.ObjectField(Texture, typeof(Texture2D), false, GUILayout.Height(18f)) as Texture2D;
    }

    public override void DrawOverlay(Rect position)
    {
        using (new MCGUI.ColorScope(T * T))
        {
            GUI.Label(position, GUIContent.none, MCGUI.Styles.WindowBackground);
        }


        float MinWindowSide = Mathf.Min(position.width, position.height);
        float XOffset = (position.width - MinWindowSide) * 0.5f;
        float YOffset = (position.height - MinWindowSide) * 0.5f;

        Handles.color = OverlayUtilities.GreenHandleColor.Dark;
        using (new GUI.GroupScope(position))
        {
            OverlayUtilities.DrawAAWireRectangle(
                1f,
                new Vector3(XOffset, YOffset, 1),
                new Vector3(XOffset + MinWindowSide, YOffset, 1),
                new Vector3(XOffset + MinWindowSide, YOffset + MinWindowSide, 1),
                new Vector3(XOffset, YOffset + MinWindowSide, 1));

            if (Texture != null)
            {
                using (new MCGUI.ColorScope(T * T))
                {
                    GUI.DrawTexture(new Rect(XOffset, YOffset, MinWindowSide, MinWindowSide), Texture);
                }
            }
        }

        Handles.color = OverlayUtilities.RedHandleColor.Dark;
        for (int I = 0; I < Indices.Length - 2; I += 3)
        {
            int I0 = Indices[I];
            int I1 = Indices[I + 1];
            int I2 = Indices[I + 2];
            Vector2 UV0 = new Vector2(position.x + XOffset, position.y + YOffset) + new Vector2(UVs[I0].x, 1f - UVs[I0].y) * MinWindowSide;
            Vector2 UV1 = new Vector2(position.x + XOffset, position.y + YOffset) + new Vector2(UVs[I1].x, 1f - UVs[I1].y) * MinWindowSide;
            Vector2 UV2 = new Vector2(position.x + XOffset, position.y + YOffset) + new Vector2(UVs[I2].x, 1f - UVs[I2].y) * MinWindowSide;
            Vector3 P0 = Vector3.Lerp(MeshAnalysisWindow.PreviewToOverlayPoint(Vertices[I0]), UV0, T).ChangeZ(1);
            Vector3 P1 = Vector3.Lerp(MeshAnalysisWindow.PreviewToOverlayPoint(Vertices[I1]), UV1, T).ChangeZ(1);
            Vector3 P2 = Vector3.Lerp(MeshAnalysisWindow.PreviewToOverlayPoint(Vertices[I2]), UV2, T).ChangeZ(1);
            OverlayUtilities.DrawTriangleWireframe(P0, P1, P2, 2f);
            //OverlayUtilities.DrawAATriangleWireframe(3f, P0, P1, P2);
        }
    }
}

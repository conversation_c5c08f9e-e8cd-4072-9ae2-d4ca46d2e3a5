using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using HightlanderSolutions.MeshCheckerEditorExtension.UserInterface;
using HightlanderSolutions.MeshCheckerEditorExtension.Utilities;
using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.Tools
{
    /// <summary>
    ///     Represents a tool which helps to view bounds of mesh.
    /// </summary>
    [Alias("Tools.ViewBounds")]
    [MeshAnalysisTool("Bounds", 201)]
    public sealed class ViewBoundsTool : MeshAnalysisTool
    {
        /// <summary>
        ///     The editor preferences key for the inner vertices drawing preference.
        /// </summary>
        private const string DrawInnerVerticesKey = "MeshAnalysis.Tool.ViewBounds.DrawInnerVertices";

        /// <summary>
        ///     The editor preferences key for the outer vertices drawing preference.
        /// </summary>
        private const string DrawOuterVerticesKey = "MeshAnalysis.Tool.ViewBounds.DrawOuterVertices";

        /// <summary>
        ///     The inflation coefficient of bounds.
        /// </summary>
        /// <remarks>
        ///     Non-inflated bounds don't contain points on sides and corners.
        /// </remarks>
        private const float Inflation = 0.0001f;

        /// <summary>
        ///     The inflated bounds of the target mesh.
        /// </summary>
        private Bounds InflatedBounds;

        /// <summary>
        ///     Vertices of the target mesh.
        /// </summary>
        private Vector3[] Vertices;

        /// <summary>
        ///     A backing field for the <see cref="DrawInnerVertices"/> property.
        /// </summary>
        private bool InnerDrawInnerVertices;

        /// <summary>
        ///     Gets or sets a value indicating whether vertices that remain within bounds should be drawn.
        /// </summary>
        private bool DrawInnerVertices
        {
            get
            {
                return InnerDrawInnerVertices;
            }

            set
            {
                InnerDrawInnerVertices = value;
                EditorPrefs.SetBool(DrawInnerVerticesKey, InnerDrawInnerVertices);
            }
        }

        /// <summary>
        ///     A backing field for the <see cref="DrawOuterVertices"/> property.
        /// </summary>
        private bool InnerDrawOuterVertices;

        /// <summary>
        ///     Gets or sets a value indicating whether vertices that not remain within bounds should be drawn.
        /// </summary>
        private bool DrawOuterVertices
        {
            get
            {
                return InnerDrawOuterVertices;
            }

            set
            {
                InnerDrawOuterVertices = value;
                EditorPrefs.SetBool(DrawOuterVerticesKey, InnerDrawOuterVertices);
            }
        }

        #region Overrides of MeshAnalysisTool

        /// <inheritdoc />
        public override void Initialize(Mesh mesh)
        {
            InflatedBounds = mesh.bounds;
            InflatedBounds.Expand(Inflation);

            Vertices = mesh.vertices;

            // Load editor preferences
            InnerDrawInnerVertices = EditorPrefs.GetBool(DrawInnerVerticesKey, false);
            InnerDrawOuterVertices = EditorPrefs.GetBool(DrawOuterVerticesKey, true);
        }

        /// <inheritdoc />
        public override void Deinitialize()
        {
            Vertices = null;
        }

        /// <inheritdoc />
        public override void DrawOverlay(Rect position)
        {
            OverlayUtilities.DrawAAWireCubeProjected(InflatedBounds, Quaternion.identity, Color.yellow);

            Handles.color = MCGUI.ErrorColor.Dark;
            foreach (Vector3 Vertex in Vertices)
            {
                Vector3 VertexScreenPosition = MeshAnalysisWindow.PreviewToOverlayPoint(Vertex).ChangeZ(-5);

                if (!position.Contains(VertexScreenPosition))
                {
                    continue;
                }

                if (InflatedBounds.Contains(Vertex))
                {
                    if (DrawInnerVertices)
                    {
                        Handles.color = OverlayUtilities.GreenHandleColor.Dark;

#if UNITY_5_6_OR_NEWER
                        Handles.SphereHandleCap(0, VertexScreenPosition, Quaternion.identity, 4, Event.current.type);
#else
                        Handles.SphereCap(0, VertexScreenPosition, Quaternion.identity, 4);
#endif
                    }
                }
                else
                {
                    if (DrawOuterVertices)
                    {
                        Handles.color = MCGUI.ErrorColor.Dark;

#if UNITY_5_6_OR_NEWER
                        Handles.SphereHandleCap(0, VertexScreenPosition, Quaternion.identity, 5, Event.current.type);
#else
                        Handles.SphereCap(0, VertexScreenPosition, Quaternion.identity, 5);
#endif
                    }
                }
            }
        }

        /// <inheritdoc />
        public override void DrawToolbar()
        {
            DrawInnerVertices = GUILayout.Toggle(DrawInnerVertices, new GUIContent("[•]"), EditorStyles.toolbarButton, GUILayout.Width(30));
            DrawOuterVertices = GUILayout.Toggle(DrawOuterVertices, new GUIContent("• [ ]"), EditorStyles.toolbarButton, GUILayout.Width(30));
        }

        #endregion
    }
}

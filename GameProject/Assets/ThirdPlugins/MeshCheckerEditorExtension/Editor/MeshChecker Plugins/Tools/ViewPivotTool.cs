using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework;
using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Plugins.Tools
{
    /// <summary>
    ///     Represents a tool which helps to view pivot of a mesh.
    /// </summary>
    [<PERSON><PERSON>("Tools.ViewPivot")]
    [MeshAnalysisTool("Pivot", 200)]
    public sealed class ViewPivotTool : MeshAnalysisTool
    {
        #region Overrides of MeshAnalysisTool

        /// <inheritdoc />
        public override void Initialize(Mesh mesh)
        {
        }

        /// <inheritdoc />
        public override void Deinitialize()
        {
        }

        /// <inheritdoc />
        public override void DrawOverlay(Rect position)
        {
            Vector3 PivotScreenPosition = MeshAnalysisWindow.PreviewToOverlayPoint(Vector3.zero);

            if (position.Contains(PivotScreenPosition))
            {
                Handles.color = Color.yellow;
                OverlayUtilities.DrawPoint(PivotScreenPosition, 5);
            }
        }

        #endregion
    }
}

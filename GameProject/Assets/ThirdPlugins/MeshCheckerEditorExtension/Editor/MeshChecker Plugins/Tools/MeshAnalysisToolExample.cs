using HightlanderSolutions.MeshCheckerEditorExtension.MeshAnalysisFramework.API;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Examples
{
    [MeshAnalysisTool("Example tool", 1000)]
    public sealed class MeshAnalysisToolExample : MeshAnalysisTool
    {
        #region Overrides of MeshAnalysisTool

        /// <inheritdoc />
        public override void Initialize(Mesh mesh)
        {
            //throw new NotImplementedException();
        }

        /// <inheritdoc />
        public override void Deinitialize()
        {
            //throw new NotImplementedException();
        }

        /// <inheritdoc />
        protected override void DrawMeshes()
        {
            //throw new NotImplementedException();
        }

        /// <inheritdoc />
        public override void DrawOverlay(Rect position)
        {
            //throw new NotImplementedException();
        }

        /// <inheritdoc />
        public override void DrawToolbar()
        {
            //throw new NotImplementedException();
        }

        #endregion
    }
}

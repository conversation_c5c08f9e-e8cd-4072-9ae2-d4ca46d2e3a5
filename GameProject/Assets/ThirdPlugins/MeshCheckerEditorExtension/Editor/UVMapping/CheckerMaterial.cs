using System;
using System.Collections.Generic;
using HightlanderSolutions.MeshCheckerEditorExtension.UserInterface;
using HightlanderSolutions.MeshCheckerEditorExtension.Utilities;
using JetBrains.Annotations;
using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Checker
{
    /// <summary>
    ///   This static class is a manager of checker material instance.
    ///   <para>
    ///     Manager controls usage of material and creates material instance when needed.
    ///     That's why you must release it in Deinitialize() or MonoBehaviour.OnDisable() call.
    ///     If don't - it is possible to get memory (resources) leak in editor.
    ///   </para>
    /// </summary>
    [PublicAPI]
    public static class CheckerMaterial
    {
        #region GUI

        /// <summary>
        ///     The size of a custom texture preview zone. 
        /// </summary>
        private const int CustomTexturePreviewSize = 50;

        /// <summary>
        ///     Default content padding for checker texture zone drawing
        /// </summary>
        [NotNull]
        private static readonly RectOffset DefaultPadding = new RectOffset(5, 5, 5, 5);

        #endregion

        /// <summary>
        ///     Requestors of checker material. Used for initialization and destroy material when needed.
        /// </summary>
        [NotNull]
        private static readonly List<Guid> Requestors = new List<Guid>();

        /// <summary>
        ///     Procedural texture of UV-Checker
        /// </summary>
        private static Texture2D ProceduralCheckerTexture;

        /// <summary>
        ///     Instance of material used for UV-Checker visualizations
        /// </summary>
        private static Material CheckerMaterialInstance;

        /// <summary>
        ///     Last used size setting for generate procedural checker texture
        /// </summary>
        private static int UsedCheckerSizeSetting;

        /// <summary>
        ///     Last used tiling setting for generate procedural checker texture
        /// </summary>
        private static float UsedCheckerTilingSetting;

        /// <summary>
        ///     Flag is enabled when material was updated since last flag disabling
        /// </summary>
        private static bool MaterialWasUpdated = false;

        #region Get & Release material

        /// <summary>
        ///     Get checker material.
        ///     <para>
        ///         Do not destroy it manually, just <see cref="Release"/> when the need has gone.
        ///     </para>
        /// </summary>
        /// <param name="requestorId">
        ///     Id of requestor. It must be passed to <see cref="Release"/> when the need has gone.
        /// </param>
        /// <returns>
        ///     Returns instance of checker material.
        /// </returns>
        /// <example>
        ///     <code>
        ///         private Material CheckerMaterialInstance;
        /// 
        ///         public void Initialize()
        ///         {
        ///             // Generate requestor id
        ///             Guid RequestorId = Guid.NewGuid();
        ///             // Get checker material instance
        ///             CheckerMaterialInstance = CheckerMaterial.Get(RequestorId);
        ///         }
        /// 
        ///         public void ProcessSomething()
        ///         {
        ///             // Apply material to something
        ///         }
        /// 
        ///         public void Deinitialize()
        ///         {
        ///             // Release checker material
        ///             CheckerMaterial.Release(RequestorId);
        ///         }
        ///     </code>
        /// </example>
        [CanBeNull]
        public static Material Get(Guid requestorId)
        {
            if (Requestors.Contains(requestorId))
            {
                Debug.LogWarning("[MeshChecker editor extension] Try to get checker material with requestorId, that already get material.");
                return CheckerMaterialInstance;
            }

            Requestors.Add(requestorId);
            UpdateInitializationState();

            return CheckerMaterialInstance;
        }

        /// <summary>
        ///     Release checker material.
        ///     <para>
        ///         <see cref="CheckerMaterial"/> will check if another request exists. If not - it will destroy checker material instance.
        ///     </para>
        /// </summary>
        /// <param name="requestorId">
        ///     Id of requestor. It must be equal to the requestorId argument in <see cref="Get"/> call.
        /// </param>
        /// <example>
        ///     <code>
        ///         private Material CheckerMaterialInstance;
        /// 
        ///         public void Initialize()
        ///         {
        ///             // Generate requestor id
        ///             Guid RequestorId = Guid.NewGuid();
        ///             // Get checker material instance
        ///             CheckerMaterialInstance = CheckerMaterial.Get(RequestorId);
        ///         }
        /// 
        ///         public void ProcessSomething()
        ///         {
        ///             // Apply material to something
        ///         }
        /// 
        ///         public void Deinitialize()
        ///         {
        ///             // Release checker material
        ///             CheckerMaterial.Release(RequestorId);
        ///         }
        ///     </code>
        /// </example>
        public static void Release(Guid requestorId)
        {
            Requestors.Remove(requestorId);
            UpdateInitializationState();
        }

        #endregion

        #region GUI

        /// <summary>
        ///     Get height of settings drawing zone
        /// </summary>
        /// <returns>
        ///     Height of drawing zone
        /// </returns>
        [PublicAPI]
        public static float GetHeight()
        {
            return GetHeight(DefaultPadding);
        }

        /// <summary>
        ///     Get height of settings drawing zone
        /// </summary>
        /// <param name="padding">Content padding</param>
        /// <returns>
        ///     Height of drawing zone
        /// </returns>
        [PublicAPI]
        public static float GetHeight([NotNull]RectOffset padding)
        {
            if (MCPreferences.CheckerTextureTypeSetting == CheckerTextureType.Procedural)
            {
                return (5 * MCGUI.LineHeight) + padding.vertical;
            }
            else
            {
                return (3 * MCGUI.LineHeight) + padding.vertical + CustomTexturePreviewSize;
            }
        }

        /// <summary>
        /// Draw checker texture settings zone
        /// </summary>
        /// <param name="position">Drawing rect</param>
        /// <param name="materialChangedCallback">Called when user changes something and material updated</param>
        [PublicAPI]
        public static void DrawSettings(Rect position, [CanBeNull] Action materialChangedCallback = null)
        {
            DrawSettings(position, DefaultPadding, materialChangedCallback);
        }

        /// <summary>
        /// Draw checker texture settings zone
        /// </summary>
        /// <param name="position">Drawing rect</param>
        /// <param name="padding">Content padding</param>
        /// <param name="materialChangedCallback">Called when user changes something and material updated</param>
        [PublicAPI]
        public static void DrawSettings(Rect position, [NotNull]RectOffset padding, [CanBeNull] Action materialChangedCallback = null)
        {
            MaterialWasUpdated = false;

            Rect ContentPosition = padding.Remove(position);
            Rect CheckerTextureTypeRect = new Rect(ContentPosition.x, ContentPosition.y, ContentPosition.width, MCGUI.LineHeight);
            MCPreferences.CheckerTextureTypeSetting = (CheckerTextureType)EditorGUI.EnumPopup(
                CheckerTextureTypeRect,
                new GUIContent("Checker texture"),
                MCPreferences.CheckerTextureTypeSetting);

            float UsedHeight = CheckerTextureTypeRect.yMax;

            Rect LabelRect = new Rect(ContentPosition.x, UsedHeight, EditorGUIUtility.labelWidth, MCGUI.LineHeight);

            switch (MCPreferences.CheckerTextureTypeSetting)
            {
                case CheckerTextureType.Procedural:
                    string CheckerSizeSliderCaption = string.Format("Texture size: {0}", Mathf.RoundToInt(Mathf.Pow(2, MCPreferences.CheckerSizeSetting)));

                    GUI.Label(LabelRect, CheckerSizeSliderCaption);

                    Rect HorizontalSliderRect = new Rect(
                        ContentPosition.x + EditorGUIUtility.labelWidth,
                        UsedHeight,
                        ContentPosition.width - EditorGUIUtility.labelWidth,
                        MCGUI.LineHeight);

                    MCPreferences.CheckerSizeSetting =
                        Mathf.RoundToInt(GUI.HorizontalSlider(HorizontalSliderRect, MCPreferences.CheckerSizeSetting, 5f, 11f));

                    UsedHeight = LabelRect.yMax;
                    LabelRect.y = UsedHeight;
                    HorizontalSliderRect.y = UsedHeight;
                    GUI.Label(LabelRect, "Pattern density: ");
                    MCPreferences.CheckerTilingSetting = Mathf.RoundToInt(GUI.HorizontalSlider(HorizontalSliderRect, MCPreferences.CheckerTilingSetting, 2f, 50f));
                    UsedHeight = LabelRect.yMax;
                    break;

                case CheckerTextureType.Custom:
                    GUI.Label(LabelRect, "Texture");
                    Rect ObjectFieldRect = new Rect(ContentPosition.x + (ContentPosition.width - CustomTexturePreviewSize), UsedHeight, CustomTexturePreviewSize, CustomTexturePreviewSize);
                    MCPreferences.CustomCheckerTextureSetting = EditorGUI.ObjectField(ObjectFieldRect, MCPreferences.CustomCheckerTextureSetting, typeof(Texture2D), false) as Texture2D;
                    UsedHeight = ObjectFieldRect.yMax + 2f;
                    break;
            }

            const int ResetButtonsWidth = 40;
            const int ResetButtonsHeight = 16;
            const int ResetButtonsOffset = 3;
            LabelRect.y = UsedHeight;
            Rect VectorFieldRect = new Rect(LabelRect.xMax, UsedHeight, ContentPosition.width - ResetButtonsWidth - ResetButtonsOffset - LabelRect.width, MCGUI.LineHeight);
            Rect ResetButtonRect = new Rect(VectorFieldRect.xMax + ResetButtonsOffset, LabelRect.y, ResetButtonsWidth, ResetButtonsHeight);

            GUI.Label(LabelRect, "UV-tiling");
            MCPreferences.CheckerTextureUVTilingSetting = EditorGUI.Vector2Field(VectorFieldRect, string.Empty, MCPreferences.CheckerTextureUVTilingSetting);


            if (GUI.Button(ResetButtonRect, "Reset", MCGUI.Styles.ToolUtilitiesPopupResetButton))
            {
                MCPreferences.CheckerTextureUVTilingSetting = Vector2.one;
            }

            UsedHeight = LabelRect.yMax;

            LabelRect.y = UsedHeight;
            VectorFieldRect.y = UsedHeight;
            ResetButtonRect.y = UsedHeight;

            GUI.Label(LabelRect, "UV-offset");
            MCPreferences.CheckerTextureUVOffsetSetting = EditorGUI.Vector2Field(VectorFieldRect, string.Empty, MCPreferences.CheckerTextureUVOffsetSetting);

            if (GUI.Button(ResetButtonRect, "Reset", MCGUI.Styles.ToolUtilitiesPopupResetButton))
            {
                MCPreferences.CheckerTextureUVOffsetSetting = Vector2.zero;
            }

            // Check material updated flag and do callback
            if (MaterialWasUpdated
                && materialChangedCallback != null)
            {
                materialChangedCallback();
            }
        }

        #endregion

        #region Initialization & Deinitialization

        private static void UpdateInitializationState()
        {
            if (Requestors.Count > 0
                && !IsInitialized)
            {
                Initialize();
            }
            else if (Requestors.Count == 0
              && IsInitialized)
            {
                Deinitialize();
            }
        }

        /// <summary>
        ///     Indicates whether <see cref="CheckerMaterial"/> is initialized.
        /// </summary>
        private static bool IsInitialized;

        private static void Initialize()
        {
            if (IsInitialized)
            {
                return;
            }

            Shader FoundShader = MCUtilities.GetBasicShader();
            CheckerMaterialInstance = new Material(FoundShader)
            {
                hideFlags = HideFlags.HideAndDontSave
            };
            UpdateMaterial();

            // Subscribe to preference changed event
            MCPreferences.Subscribe(PreferenceType.CheckerSize, UpdateMaterial);
            MCPreferences.Subscribe(PreferenceType.CheckerProceduralTiling, UpdateMaterial);
            MCPreferences.Subscribe(PreferenceType.CheckerTextureType, UpdateMaterial);
            MCPreferences.Subscribe(PreferenceType.CustomCheckerTexturePath, UpdateMaterial);
            MCPreferences.Subscribe(PreferenceType.CheckerTextureUVTiling, UpdateMaterial);
            MCPreferences.Subscribe(PreferenceType.CheckerTextureUVOffset, UpdateMaterial);

            IsInitialized = true;
        }

        private static void Deinitialize()
        {
            if (!IsInitialized)
            {
                return;
            }

            UnityEngine.Object.DestroyImmediate(CheckerMaterialInstance);
            if (ProceduralCheckerTexture != null)
            {
                UnityEngine.Object.DestroyImmediate(ProceduralCheckerTexture);
            }

            // Unsubscribe from preference changed event
            MCPreferences.Unsubscribe(PreferenceType.CheckerSize, UpdateMaterial);
            MCPreferences.Unsubscribe(PreferenceType.CheckerProceduralTiling, UpdateMaterial);
            MCPreferences.Unsubscribe(PreferenceType.CheckerTextureType, UpdateMaterial);
            MCPreferences.Unsubscribe(PreferenceType.CustomCheckerTexturePath, UpdateMaterial);
            MCPreferences.Unsubscribe(PreferenceType.CheckerTextureUVTiling, UpdateMaterial);
            MCPreferences.Unsubscribe(PreferenceType.CheckerTextureUVOffset, UpdateMaterial);

            IsInitialized = false;
        }

        #endregion

        /// <summary>
        ///     Generate new UV-Checker texture
        /// </summary>
        /// <returns>New UV-Checker texture.</returns>
        [NotNull]
        private static Texture2D GenerateCheckerTexture()
        {
            // Save used procedural checker texture settings
            UsedCheckerSizeSetting = MCPreferences.CheckerSizeSetting;
            UsedCheckerTilingSetting = MCPreferences.CheckerTilingSetting;

            int Size = Mathf.RoundToInt(Mathf.Pow(2, UsedCheckerSizeSetting));
            Texture2D NewCheckerTexture = new Texture2D(Size, Size)
            {
                hideFlags = HideFlags.HideAndDontSave,
                mipMapBias = -0.5f
            };
            float TextureTiling = Mathf.Floor(UsedCheckerTilingSetting / 2f) * 2;

            Color[] Colors = new Color[Size * Size];

            Color[] WhiteStartLine = new Color[Size];
            Color[] BlackStartLine = new Color[Size];
            float SizeToTextureTiling = Size / TextureTiling;

            // Create color arrays for base lines
            for (int X = 0; X < Size; X++)
            {
                bool EvenRect = Mathf.FloorToInt(X / SizeToTextureTiling) % 2 == 0;
                WhiteStartLine[X] = EvenRect ? Color.white : Color.black;
                BlackStartLine[X] = EvenRect ? Color.black : Color.white;
            }

            // Fill colors array with base lines
            for (int Y = 0; Y < Size; Y++)
            {
                bool EvenLine = Mathf.FloorToInt(Y / SizeToTextureTiling) % 2 == 0;
                if (EvenLine)
                {
                    BlackStartLine.CopyTo(Colors, Y * Size);
                }
                else
                {
                    WhiteStartLine.CopyTo(Colors, Y * Size);
                }
            }

            NewCheckerTexture.SetPixels(Colors);
            NewCheckerTexture.Apply();

            return NewCheckerTexture;
        }

        /// <summary>
        ///     Update checker material and generate new texture
        /// </summary>
        /// <exception cref="InvalidOperationException">The checker material not exist.</exception>
        private static void UpdateMaterial()
        {
            if (CheckerMaterialInstance == null)
            {
                throw new InvalidOperationException("The checker material not exist.");
            }

            // Destroy old procedural texture and generate new if needed.
            bool NeedToGenerateProceduralTexture = (MCPreferences.CheckerTextureTypeSetting == CheckerTextureType.Procedural)
                                                   && ((ProceduralCheckerTexture == null)
                                                       || (UsedCheckerSizeSetting != MCPreferences.CheckerSizeSetting)
                                                       || (UsedCheckerTilingSetting != MCPreferences.CheckerTilingSetting));
            if (NeedToGenerateProceduralTexture)
            {
                if (ProceduralCheckerTexture != null)
                {
                    CheckerMaterialInstance.mainTexture = null;

                    // Destroy old procedural texture
                    UnityEngine.Object.DestroyImmediate(ProceduralCheckerTexture);
                }

                // Generate new procedural texture
                ProceduralCheckerTexture = GenerateCheckerTexture();
            }

            // Setup material
            if (CheckerMaterialInstance.HasProperty("_MainTex"))
            {
                if (MCPreferences.CheckerTextureTypeSetting == CheckerTextureType.Procedural)
                {
                    // Attach procedural texture to material
                    CheckerMaterialInstance.mainTexture = ProceduralCheckerTexture;
                }
                else if (MCPreferences.CheckerTextureTypeSetting == CheckerTextureType.Custom)
                {
                    // Attach custom texture to material
                    CheckerMaterialInstance.mainTexture = MCPreferences.CustomCheckerTextureSetting;
                }

                CheckerMaterialInstance.mainTextureScale = MCPreferences.CheckerTextureUVTilingSetting;
                CheckerMaterialInstance.mainTextureOffset = MCPreferences.CheckerTextureUVOffsetSetting;
            }

            MaterialWasUpdated = true;
        }
    }
}

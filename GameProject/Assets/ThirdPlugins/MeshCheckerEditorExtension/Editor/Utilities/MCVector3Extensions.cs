using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Utilities
{
    /// <summary>
    ///     Provides extension methods for <see cref="Vector3"/>.
    /// </summary>
    public static class MCVector3Extensions
    {
        /// <summary>
        ///     Scales vector.
        /// </summary>
        /// <param name="vector">The target vector.</param>
        /// <param name="scale">The scale.</param>
        /// <returns>The scaled vector.</returns>
        [Pure]
        public static Vector3 ApplyScale(this Vector3 vector, Vector3 scale)
        {
            return new Vector3(vector.x * scale.x, vector.y * scale.y, vector.z * scale.z);
        }

        /// <summary>
        ///     Divides one by the specified vector.
        /// </summary>
        /// <param name="vector">The target vector.</param>
        /// <returns>A vector with components equal to 1f divided by vector component.</returns>
        [Pure]
        public static Vector3 GetOneDividedBy(this Vector3 vector)
        {
            return new Vector3(1f / vector.x, 1f / vector.y, 1f / vector.z);
        }

        /// <summary>
        ///     Changes the X value of the target vector.
        /// </summary>
        /// <param name="vector">The target vector.</param>
        /// <param name="x">New X value.</param>
        /// <returns>Vector with specified X coordinate.</returns>
        [Pure]
        public static Vector3 ChangeX(this Vector3 vector, float x)
        {
            return new Vector3(x, vector.y, vector.z);
        }

        /// <summary>
        ///     Changes the Y value of the target vector.
        /// </summary>
        /// <param name="vector">The target vector.</param>
        /// <param name="y">New Y value.</param>
        /// <returns>Vector with specified Y coordinate.</returns>
        [Pure]
        public static Vector3 ChangeY(this Vector3 vector, float y)
        {
            return new Vector3(vector.x, y, vector.z);
        }

        /// <summary>
        ///     Changes the Z value of the target vector.
        /// </summary>
        /// <param name="vector">The target vector.</param>
        /// <param name="z">New Z value.</param>
        /// <returns>Vector with specified Z coordinate.</returns>
        [Pure]
        public static Vector3 ChangeZ(this Vector3 vector, float z)
        {
            return new Vector3(vector.x, vector.y, z);
        }

        /// <summary>
        ///     Changes the X value of the target vector.
        /// </summary>
        /// <param name="vector">The target vector.</param>
        /// <param name="x">New X value.</param>
        /// <returns>Vector with specified X coordinate.</returns>
        [Pure]
        public static Vector2 ChangeX(this Vector2 vector, float x)
        {
            return new Vector2(x, vector.y);
        }

        /// <summary>
        ///     Changes the Y value of the target vector.
        /// </summary>
        /// <param name="vector">The target vector.</param>
        /// <param name="y">New Y value.</param>
        /// <returns>Vector with specified Y coordinate.</returns>
        [Pure]
        public static Vector2 ChangeY(this Vector2 vector, float y)
        {
            return new Vector2(vector.x, y);
        }

        /// <summary>
        ///     Gets maximal component of a vector.
        /// </summary>
        /// <param name="vector">The target.</param>
        /// <returns>Maximal component of the vector.</returns>
        public static float GetMaxComponent(this Vector3 vector)
        {
            return Mathf.Max(vector.x, vector.y, vector.z);
        }
    }
}
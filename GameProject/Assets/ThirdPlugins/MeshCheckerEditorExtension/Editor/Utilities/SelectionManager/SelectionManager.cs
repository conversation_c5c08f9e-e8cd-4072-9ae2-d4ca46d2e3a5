using System;
using System.Collections.Generic;
using System.Linq;
using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension
{
    /// <summary>
    ///     Represents a manager of selection.
    /// </summary>
    /// <typeparam name="TObject">The type of selection objects.</typeparam>
    /// <typeparam name="TComponent">The type of the needed component.</typeparam>
    internal class SelectionManager<TObject, TComponent>
#if UNITY_4_6
        where TComponent : Component
#else
        where TComponent : class
#endif
        where TObject : class
    {
        /// <summary>
        ///     Raised when some of objects has been deselected.
        /// </summary>
        public event Action<List<TObject>> ObjectsDeselectedEvent;

        /// <summary>
        ///     Raised when objects has been selected.
        /// </summary>
        public event Action<List<TObject>> ObjectsSelectedEvent;

        /// <summary>
        ///     A backing field for the <see cref="SelectedObjects"/> property.
        /// </summary>
        [NotNull]
        private TObject[] InnerSelectedObjects = new TObject[0];

        /// <summary>
        ///     Gets the array of a selected objects.
        /// </summary>
        [NotNull]
        public TObject[] SelectedObjects
        {
            get
            {
                return InnerSelectedObjects;
            }

            private set
            {
                InnerSelectedObjects = value;
            }
        }

        /// <summary>
        ///     Gets a list of currently selected components.
        /// </summary>
        [NotNull]
        public List<TComponent> SelectedComponents
        {
            get
            {
                return SelectedComponentsDictionary.Values.ToList();
            }
        }

        /// <summary>
        ///     Gets a value indicating whether component exists in selection.
        /// </summary>
        public bool IsComponentExistInSelection
        {
            get
            {
                return SelectedComponents.Count > 0;
            }
        }

        /// <summary>
        ///     The dictionary of selected components and their owners.
        /// </summary>
        [NotNull]
        protected readonly Dictionary<TObject, TComponent> SelectedComponentsDictionary = new Dictionary<TObject, TComponent>();

        /// <summary>
        ///     Finds a component on each object in list.
        /// </summary>
        /// <param name="objects">The collection of owners.</param>
        /// <returns>The list of components which finded on owners.</returns>
        /// <exception cref="ArgumentNullException"><paramref name="objects"/> is <see langword="null"/></exception>
        [NotNull]
        public static List<TComponent> GetComponentsFromObjects([NotNull]List<TObject> objects)
        {
            if (objects == null)
            {
                throw new ArgumentNullException("objects");
            }

            List<TComponent> ResultList = new List<TComponent>();

            foreach (TObject IterationObject in objects)
            {
                if (IterationObject == null)
                {
                    continue;
                }

                TComponent FindedComponent;

                if (TryGetComponentFromGameObject(IterationObject, out FindedComponent)
                    || TryGetComponentFromComponent(IterationObject, out FindedComponent)
                    || TryGetTargetAsComponent(IterationObject, out FindedComponent))
                {
                    ResultList.Add(FindedComponent);
                }
            }

            return ResultList;
        }

        /// <summary>
        ///     Checks changes and updates selection if needed.
        /// </summary>
        /// <param name="selectedObjects">The array of currently selected objects.</param>
        /// <returns><c>true</c> if selection changed; otherwise, <c>false</c>.</returns>
        /// <exception cref="ArgumentNullException"><paramref name="selectedObjects"/> is <see langword="null"/></exception>
        public bool CheckAndChangeSelection([NotNull]TObject[] selectedObjects)
        {
            if (selectedObjects == null)
            {
                throw new ArgumentNullException("selectedObjects");
            }

            // If selection doesn't changed -> return
            if (SelectedObjects.SequenceEqual(selectedObjects))
            {
                return false;
            }

            // Save previous selection
            TObject[] PreviousSelection = SelectedObjects;

            // Update current selection
            SelectedObjects = selectedObjects;

            DeselectDisappearedObjects(PreviousSelection);

            SelectNewObjects(PreviousSelection);

            return true;
        }

        /// <summary>
        ///     Clears the selection.
        /// </summary>
        public void ClearSelection()
        {
            CheckAndChangeSelection(new TObject[0]);
        }

        /// <summary>
        ///     Gets components by their owners.
        /// </summary>
        /// <param name="keysCollection">The collection of owners.</param>
        /// <returns>The dictionary of owners and their components.</returns>
        /// <exception cref="ArgumentNullException"><paramref name="keysCollection"/> is <see langword="null"/></exception>
        [NotNull]
        public Dictionary<TObject, TComponent> GetPairsByKeys([NotNull]IEnumerable<TObject> keysCollection)
        {
            if (keysCollection == null)
            {
                throw new ArgumentNullException("keysCollection");
            }

            return keysCollection.Where(key => key != null && SelectedComponentsDictionary.ContainsKey(key))
                .ToDictionary(key => key, key => SelectedComponentsDictionary[key]);
        }

        /// <summary>
        ///     Tries to get the TComponent target as a component.
        /// </summary>
        /// <param name="target">Target TComponent.</param>
        /// <param name="component">The casted component. 
        ///     <para>
        ///         <see langword="null" /> if casting failed.
        ///     </para>
        /// </param>
        /// <returns><c>true</c> if success; otherwise, <c>false</c>.</returns>
        /// <exception cref="ArgumentNullException"><paramref name="target"/> is <see langword="null"/></exception>
        protected static bool TryGetTargetAsComponent([NotNull]TObject target, [CanBeNull]out TComponent component)
        {
            if (target == null)
            {
                throw new ArgumentNullException("target");
            }

            // Try cast to TComponent
            component = target as TComponent;
            if (component != null)
            {
                return true;
            }

            return false;
        }

        /// <summary>
        ///     Tries to get a component from the <see cref="Component"/> target.
        /// </summary>
        /// <param name="target">Target <see cref="Component"/>.</param>
        /// <param name="component">The finded component. 
        ///     <para>
        ///         <see langword="null" /> if not finded.
        ///     </para>
        /// </param>
        /// <returns><c>true</c> if finded; otherwise, <c>false</c>.</returns>
        /// <exception cref="ArgumentNullException"><paramref name="target"/> is <see langword="null"/></exception>
        protected static bool TryGetComponentFromComponent([NotNull]TObject target, [CanBeNull]out TComponent component)
        {
            if (target == null)
            {
                throw new ArgumentNullException("target");
            }

            component = null;

            // Try cast to Component
            Component SelectedObjectAsComponent = target as Component;
            if (SelectedObjectAsComponent != null && !SelectedObjectAsComponent.Equals(null))
            {
                // Try find a TComponent component on newly selected object
                TComponent FindedComponent = SelectedObjectAsComponent.GetComponent<TComponent>();
                if (FindedComponent != null && !FindedComponent.Equals(null))
                {
                    component = FindedComponent;
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        ///     Tries to get a component from the <see cref="GameObject"/> target.
        /// </summary>
        /// <param name="target">Target <see cref="GameObject"/>.</param>
        /// <param name="component">The finded component. 
        ///     <para>
        ///         <see langword="null" /> if not finded.
        ///     </para>
        /// </param>
        /// <returns><c>true</c> if finded; otherwise, <c>false</c>.</returns>
        /// <exception cref="ArgumentNullException"><paramref name="target"/> is <see langword="null"/></exception>
        protected static bool TryGetComponentFromGameObject([NotNull]TObject target, [CanBeNull]out TComponent component)
        {
            if (target == null)
            {
                throw new ArgumentNullException("target");
            }

            component = null;

            // Try cast to GameObject
            GameObject SelectedObjectAsObject = target as GameObject;
            if (SelectedObjectAsObject != null)
            {
                // Try find a TComponent component on newly selected object
                TComponent FindedComponent = SelectedObjectAsObject.GetComponent<TComponent>();
                if (FindedComponent != null && !FindedComponent.Equals(null))
                {
                    component = FindedComponent;
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        ///     Tries to find a <typeparamref name="TComponent"/> in the selected <typeparamref name="TObject"/> and add it to the <see cref="SelectedComponentsDictionary"/>.
        /// </summary>
        /// <param name="selectedObject">The newly selected object.</param>
        protected virtual void TryAddComponentToSelection([NotNull] TObject selectedObject)
        {
            TComponent FindedComponent;

            if (TryGetComponentFromGameObject(selectedObject, out FindedComponent)
                || TryGetComponentFromComponent(selectedObject, out FindedComponent)
                || TryGetTargetAsComponent(selectedObject, out FindedComponent))
            {
                // Add to the SelectedComponentsDictionary dictionary
                SelectedComponentsDictionary.Add(selectedObject, FindedComponent);
            }
        }

        /// <summary>
        ///     Selects objects that doesn't exist in previous selection.
        /// </summary>
        /// <param name="previousSelection">The previous selection.</param>
        /// <exception cref="ArgumentNullException"><paramref name="previousSelection"/> is <see langword="null"/></exception>
        private void SelectNewObjects([NotNull]TObject[] previousSelection)
        {
            if (previousSelection == null)
            {
                throw new ArgumentNullException("previousSelection");
            }

            List<TObject> NewlySelectedObjects = new List<TObject>();

            foreach (TObject SelectedObject in SelectedObjects)
            {
                if (SelectedObject == null)
                {
                    continue;
                }

                // If the object doesn't exist in previous selection.
                if (!previousSelection.Contains(SelectedObject))
                {
                    // Add to the newly selected objects list.
                    NewlySelectedObjects.Add(SelectedObject);

                    TryAddComponentToSelection(SelectedObject);
                }
            }

            if (NewlySelectedObjects.Count > 0)
            {
                CallObjectsSelectedEvent(NewlySelectedObjects);
            }
        }

        /// <summary>
        ///     Deselects objects that don't exist in selection.
        /// </summary>
        /// <param name="previousSelection">The previous selection.</param>
        private void DeselectDisappearedObjects([NotNull]TObject[] previousSelection)
        {
            // Deselect previous, if selection lost.
            List<TObject> DeselectedObjects = new List<TObject>();

            foreach (TObject PreviousSelectedObject in previousSelection)
            {
                if (PreviousSelectedObject == null)
                {
                    continue;
                }

                // If object lost selection -> add to list
                if (!SelectedObjects.Contains(PreviousSelectedObject))
                {
                    DeselectedObjects.Add(PreviousSelectedObject);

                    // Remove from the SelectedComponentsDictionary dictionary
                    SelectedComponentsDictionary.Remove(PreviousSelectedObject);
                }
            }

            // If some objects deselected then try call event
            if (DeselectedObjects.Count > 0)
            {
                CallObjectsDeselectedEvent(DeselectedObjects);
            }
        }

        /// <summary>
        ///     Checks an <see cref="ObjectsDeselectedEvent"/> for null and calls it.
        /// </summary>
        /// <param name="objects">Deselected objects.</param>
        /// <exception cref="ArgumentNullException"><paramref name="objects"/> is <see langword="null"/></exception>
        private void CallObjectsDeselectedEvent([NotNull]List<TObject> objects)
        {
            if (objects == null)
            {
                throw new ArgumentNullException("objects");
            }

            if (objects.Count == 0)
            {
                return;
            }

            if (ObjectsDeselectedEvent != null)
            {
                try
                {
                    ObjectsDeselectedEvent(objects);
                }
                catch (Exception E)
                {
                    Debug.LogException(E);
                }
            }
        }

        /// <summary>
        ///     Checks an <see cref="ObjectsSelectedEvent"/> for null and calls it.
        /// </summary>
        /// <param name="objects">Selected objects.</param>
        /// <exception cref="ArgumentNullException"><paramref name="objects"/> is <see langword="null"/></exception>
        private void CallObjectsSelectedEvent([NotNull]List<TObject> objects)
        {
            if (objects == null)
            {
                throw new ArgumentNullException("objects");
            }

            if (objects.Count == 0)
            {
                return;
            }

            if (ObjectsSelectedEvent != null)
            {
                try
                {
                    ObjectsSelectedEvent(objects);
                }
                catch (Exception E)
                {
                    Debug.LogException(E);
                }
            }
        }
    }
}
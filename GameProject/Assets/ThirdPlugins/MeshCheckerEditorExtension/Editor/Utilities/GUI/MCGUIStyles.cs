using System;

using JetBrains.Annotations;
using UnityEditor;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.UserInterface
{
    /// <summary>
    ///     Provides styles, which used in MeshChecker editor extension.
    /// </summary>
    [PublicAPI]
    public sealed partial class MCGUIStyles : IDisposable
    {
        /// <summary>
        ///     Gets the style of a left large button.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public GUIStyle LargeButtonLeft
        {
            get
            {
                if (InnerLargeButtonLeft == null)
                {
                    InnerLargeButtonLeft = new GUIStyle(LargeButtonLeftKey)
                        .SetPadding(new RectOffset(3, 3, 3, 3))
                        .SetImagePosition(ImagePosition.ImageAbove)
                        .SetFixedHeight(40f);
                }

                return InnerLargeButtonLeft;
            }
        }

        /// <summary>
        ///     Gets the style of a middle large button.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public GUIStyle LargeButtonMiddle
        {
            get
            {
                if (InnerLargeButtonMiddle == null)
                {
                    InnerLargeButtonMiddle = new GUIStyle(LargeButtonMiddleKey)
                        .SetPadding(new RectOffset(3, 3, 3, 3))
                        .SetImagePosition(ImagePosition.ImageAbove)
                        .SetFixedHeight(40f);
                }

                return InnerLargeButtonMiddle;
            }
        }

        /// <summary>
        ///     Gets the style of a right large button.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public GUIStyle LargeButtonRight
        {
            get
            {
                if (InnerLargeButtonRight == null)
                {
                    InnerLargeButtonRight = new GUIStyle(LargeButtonRightKey)
                        .SetPadding(new RectOffset(3, 3, 3, 3))
                        .SetImagePosition(ImagePosition.ImageAbove)
                        .SetFixedHeight(40f);
                }

                return InnerLargeButtonRight;
            }
        }

        /// <summary>
        ///     Gets the style of a left small button.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public GUIStyle SmallButtonLeft
        {
            get
            {
                if (InnerSmallButtonLeft == null)
                {
                    InnerSmallButtonLeft = new GUIStyle(SmallButtonLeftKey)
                        .SetPadding(new RectOffset(3, 3, 3, 3))
                        .SetImagePosition(ImagePosition.ImageAbove)
                        .SetFixedHeight(20);
                }

                return InnerSmallButtonLeft;
            }
        }

        /// <summary>
        ///     Gets the style of a middle small button.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public GUIStyle SmallButtonMiddle
        {
            get
            {
                if (InnerSmallButtonMiddle == null)
                {
                    InnerSmallButtonMiddle = new GUIStyle(SmallButtonMiddleKey)
                        .SetPadding(new RectOffset(3, 3, 3, 3))
                        .SetImagePosition(ImagePosition.ImageAbove)
                        .SetFixedHeight(20);
                }

                return InnerSmallButtonMiddle;
            }
        }

        /// <summary>
        ///     Gets the style of a right small button.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public GUIStyle SmallButtonRight
        {
            get
            {
                if (InnerSmallButtonRight == null)
                {
                    InnerSmallButtonRight = new GUIStyle(SmallButtonRightKey)
                        .SetPadding(new RectOffset(3, 3, 3, 3))
                        .SetImagePosition(ImagePosition.ImageAbove)
                        .SetFixedHeight(20);
                }

                return InnerSmallButtonRight;
            }
        }

        /// <summary>
        ///     The style of a middle button.
        /// </summary>
        [PublicAPI]
        public readonly GUIStyle ButtonMiddle;

        /// <summary>
        ///     The style of a window background.
        /// </summary>
        [PublicAPI]
        public readonly GUIStyle WindowBackground;

        /// <summary>
        ///     The style of a settings button.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public readonly GUIStyle SettingsButton;

        /// <summary>
        ///     The style of the refresh button.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public readonly GUIStyle RefreshButton;

        /// <summary>
        ///     The style of a help box.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public readonly GUIStyle HelpBox;

        /// <summary>
        ///     The style of a notification message.
        /// </summary>
        /// <remarks>
        ///     This style is just tweaked builtin 'GroupBox' style.
        /// </remarks>
        [PublicAPI]
        [NotNull]
        public readonly GUIStyle NotificationMessage;

        /// <summary>
        ///     Gets the style of a colored state line.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public readonly GUIStyle ColoredVerticalLine;

        /// <summary>
        ///     The word-wrapped label style.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public readonly GUIStyle LabelWordWrap;

        /// <summary>
        ///     The simple label style.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public readonly GUIStyle ToolEmptyLabel;

        /// <summary>
        ///     The label style with roboto font.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public readonly GUIStyle RobotoLabel;

        /// <summary>
        ///     The label style with roboto font (Centered).
        /// </summary>
        [PublicAPI]
        [NotNull]
        public readonly GUIStyle RobotoLabelCentered;

        /// <summary>
        ///     The label style with bold roboto font.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public readonly GUIStyle RobotoLabelBold;

        /// <summary>
        ///     The label style with white bold roboto font.
        /// </summary>
        [PublicAPI]
        [NotNull]
        public readonly GUIStyle RobotoLabelBoldWhite;

        /// <summary>
        ///     Gets the style of a selection box which can be used to display the hover state.
        ///     <para>
        ///         This style display a white box and, because of this, should be used with the <see cref="MCGUI.ColorScope"/>
        ///         and <see cref="MCGUI.SelectionColor"/>.
        ///     </para>
        /// </summary>
        [NotNull]
        internal GUIStyle SelectionBox { get; private set; }

        /// <summary>
        ///     Gets the style of an even label.
        /// </summary>
        [NotNull]
        internal GUIStyle ListLabelEven
        {
            get
            {
                if (InnerListLabelEven == null)
                {
                    InnerListLabelEven = new GUIStyle(ListLabelEvenKey)
                  .SetPadding(new RectOffset(2, 2, 2, 2))
                  .SetOverflow(new RectOffset(0, 0, 0, 0));
                    InnerListLabelEven.richText = true;
                }

                return InnerListLabelEven;
            }
        }

        /// <summary>
        ///     Gets the style of an odd label.
        /// </summary>
        [NotNull]
        internal GUIStyle ListLabelOdd
        {
            get
            {
                if (InnerListLabelOdd == null)
                {
                    InnerListLabelOdd = new GUIStyle(ListLabelOddKey)
                        .SetPadding(new RectOffset(2, 2, 2, 2))
                        .SetOverflow(new RectOffset(0, 0, 0, 0));
                    InnerListLabelOdd.richText = true;
                }

                return InnerListLabelOdd;
            }
        }

        /// <summary>
        ///     The style of an expand button.
        /// </summary>
        internal readonly GUIStyle ExpandButton;

        /// <summary>
        ///     The style of a collapse button.
        /// </summary>
        internal readonly GUIStyle CollapseButton;

        /// <summary>
        ///     The style of «flow background».
        /// </summary>
        public readonly GUIStyle FlowBackground;

        /// <summary>
        ///     The style of an error label.
        /// </summary>
        internal readonly GUIStyle ErrorLabel;

        /// <summary>
        ///     The style used when bounds sizes are drawn in the scene view.
        /// </summary>
        internal readonly GUIStyle SceneAxisLabelX;

        /// <summary>
        ///     The style used when bounds sizes are drawn in the scene view.
        /// </summary>
        internal readonly GUIStyle SceneAxisLabelY;

        /// <summary>
        ///     The style used when bounds sizes are drawn in the scene view.
        /// </summary>
        internal readonly GUIStyle SceneAxisLabelZ;

        private const string LargeButtonLeftKey = "LargeButtonLeft";
        private const string LargeButtonMiddleKey = "LargeButtonMid";
        private const string LargeButtonRightKey = "LargeButtonRight";
        private const string SmallButtonLeftKey = "LargeButtonLeft";
        private const string SmallButtonMiddleKey = "LargeButtonMid";
        private const string SmallButtonRightKey = "LargeButtonRight";
        private const string ButtonMiddleKey = "ButtonMid";
        private const string ListLabelEvenKey = "OL EntryBackEven";
        private const string ListLabelOddKey = "OL EntryBackOdd";
        private const string ErrorLabelKey = "ErrorLabel";
        private const string FlowBackgroundKey = "flow background";
#if !UNITY_2018_3_OR_NEWER
        private const string FlowShaderOutOffKey = "flow shader out 0";
        private const string FlowShaderOutOnKey = "flow shader out 2";
        private const string FlowShaderOutOnErrorKey = "flow shader out 5";
#endif
        private const string ButtonKey = "button";

        private GUIStyle InnerLargeButtonLeft;
        private GUIStyle InnerLargeButtonMiddle;
        private GUIStyle InnerLargeButtonRight;
        private GUIStyle InnerSmallButtonLeft;
        private GUIStyle InnerSmallButtonMiddle;
        private GUIStyle InnerSmallButtonRight;
        private GUIStyle InnerListLabelEven;
        private GUIStyle InnerListLabelOdd;
        private GUIStyle InnerToolUtilitiesPopupResetButton;

        /// <summary>
        ///     Gets the style of a separator line.
        /// </summary>
        [NotNull]
        internal GUIStyle SeparatorLine { get; private set; }

        /// <summary>
        ///     Gets the style of a colored state line.
        /// </summary>
        [NotNull]
        internal GUIStyle ColoredStateLine { get; private set; }

        /// <summary>
        ///     Gets the style of reset button.
        /// </summary>
        [NotNull]
        internal GUIStyle ToolUtilitiesPopupResetButton
        {
            get
            {
                if (InnerToolUtilitiesPopupResetButton == null)
                {
                    InnerToolUtilitiesPopupResetButton = new GUIStyle(ButtonKey)
                                                            .SetPadding(new RectOffset(2, 2, 0, 0));
                }

                return InnerToolUtilitiesPopupResetButton;
            }
        }

        /// <summary>
        ///     The color which used as window background.
        /// </summary>
        private readonly SkinDependentItem<Color> WindowBackgroundColor = new SkinDependentItem<Color>(
            new Color(0.76f, 0.76f, 0.76f),
            new Color(0.219f, 0.219f, 0.219f));

        /// <summary>
        ///     The color which used as border of window background.
        /// </summary>
        private readonly SkinDependentItem<Color> WindowBackgroundBorderColor = new SkinDependentItem<Color>(
            new Color(0.333f, 0.333f, 0.333f),
            new Color(0.101f, 0.101f, 0.101f));

        #region Implementation of IDisposable

        /// <inheritdoc />
        public void Dispose()
        {
#if !UNITY_2019_1_OR_NEWER
            UnityEngine.Object.DestroyImmediate(WindowBackground.normal.background);
#endif
        }

        #endregion

        /// <summary>
        ///     Initializes a new instance of the <see cref="MCGUIStyles"/> class.
        /// </summary>
        internal MCGUIStyles()
        {
            ButtonMiddle = new GUIStyle(ButtonMiddleKey);

#if UNITY_2019_1_OR_NEWER
            WindowBackground = new GUIStyle("OL box flat");
#else
            WindowBackground = new GUIStyle
            {
                name = "MC.WindowBackground",
                normal = new GUIStyleState
                {
                    background = MCGUI.TextureUtilities.CreateBorderedTexture(WindowBackgroundColor.Current, WindowBackgroundBorderColor.Current)
                },
                border = new RectOffset(2, 17, 2, 17),
                overflow = new RectOffset(1, 1, 1, 1)
            };
#endif
            
            FlowBackground = new GUIStyle(FlowBackgroundKey);
#if !UNITY_2018_3_OR_NEWER
#pragma warning disable 618
            FlowShaderOutOff = new GUIStyle(FlowShaderOutOffKey);
            FlowShaderOutOn = new GUIStyle(FlowShaderOutOnKey);
            FlowShaderOutOnError = new GUIStyle(FlowShaderOutOnErrorKey);
#pragma warning restore 618
#endif

            SelectionBox = new GUIStyle
            {
                normal = { background = MCGUI.LoadIcon("mc-border-2px_white.png") },
                border = new RectOffset(3, 3, 3, 3),
                margin = new RectOffset(0, 0, 0, 0),
                stretchWidth = true,
                stretchHeight = true
            };

#if !UNITY_4_6 && !UNITY_4_7 // Analog for UNITY_5_0_OR_NEWER
            HelpBox = new GUIStyle(EditorStyles.helpBox);
#else
            HelpBox = new GUIStyle("HelpBox");
#endif

            ErrorLabel = new GUIStyle(ErrorLabelKey);

            SeparatorLine = new GUIStyle
            {
                normal = new GUIStyleState { background = EditorGUIUtility.whiteTexture },
                stretchWidth = true,
                margin = new RectOffset(0, 0, 2, 2),
            };

            SettingsButton = new GUIStyle
            {
                normal = new GUIStyleState
                {
                    background = EditorGUIUtility.isProSkin ? MCGUI.LoadIcon("Settings_medium_darktheme.png") : MCGUI.LoadIcon("Settings_medium.png")
                },
                active = new GUIStyleState
                {
                    background = !EditorGUIUtility.isProSkin ? MCGUI.LoadIcon("Settings_medium_darktheme.png") : MCGUI.LoadIcon("Settings_medium.png")
                }
            };

            RefreshButton = new GUIStyle
            {
                normal = new GUIStyleState
                {
                    background = EditorGUIUtility.FindTexture("RotateTool")
                },
                active = new GUIStyleState
                {
                    background = EditorGUIUtility.FindTexture("RotateTool On")
                },
#if UNITY_2019_1_OR_NEWER
                margin = new RectOffset(3, 3, 3, 3)
#endif
            };

            ExpandButton = new GUIStyle
            {
                normal = new GUIStyleState { background = MCGUI.Icons.ExpandIcon }
            };

            CollapseButton = new GUIStyle
            {
                normal = new GUIStyleState { background = MCGUI.Icons.CollapseIcon }
            };

            ColoredStateLine = new GUIStyle
            {
                normal = { background = MCGUI.Icons.StateTexture },
                stretchHeight = true,
                stretchWidth = true,
                border = new RectOffset(2, 2, 2, 2)
            };

            ColoredVerticalLine = new GUIStyle
            {
                normal = { background = MCGUI.Icons.VerticalLineTexture },
                stretchHeight = true,
                stretchWidth = true,
                border = new RectOffset(2, 2, 2, 2)
            };

            NotificationMessage = new GUIStyle("GroupBox")
            {
                padding = new RectOffset(5, 5, 5, 5),
                margin = new RectOffset(0, 0, 0, 0),
                wordWrap = true,
                richText = true
            };

            LabelWordWrap = new GUIStyle("Label")
            {
                wordWrap = true,
                richText = true
            };

            ToolEmptyLabel = new GUIStyle("Label")
            {
                wordWrap = true,
                richText = true,
                alignment = TextAnchor.MiddleCenter,
                fontSize = 16
            };

            RobotoLabel = new GUIStyle("Label")
            {
                wordWrap = true,
                richText = true,
                alignment = TextAnchor.UpperLeft
            };

            RobotoLabelCentered = new GUIStyle("Label")
            {
                wordWrap = true,
                richText = true,
                alignment = TextAnchor.MiddleCenter
            };

            Font RobotoRegular = MCGUI.LoadFont("Fonts/Roboto-Regular.ttf");
            if (RobotoRegular != null)
            {
                RobotoLabel.SetFont(RobotoRegular, 15);
                RobotoLabelCentered.SetFont(RobotoRegular, 15);
            }

            Font RobotoBold = MCGUI.LoadFont("Fonts/Roboto-Bold.ttf");
            if (RobotoBold != null)
            {
                ToolEmptyLabel.SetFont(RobotoBold, 16);

                RobotoLabelBold = new GUIStyle("Label")
                {
                    wordWrap = true,
                    richText = true,
                    alignment = TextAnchor.UpperLeft,
                    font = RobotoBold,
                    fontSize = 15
                };
            }
            else
            {
                RobotoLabelBold = new GUIStyle("BoldLabel")
                {
                    wordWrap = true,
                    richText = true,
                    alignment = TextAnchor.UpperLeft,
                };
            }

            RobotoLabelBoldWhite = new GUIStyle(RobotoLabelBold)
            {
                normal = { textColor = new Color(1, 1, 1, 0.9f) }
            };
            
            SceneAxisLabelX = new GUIStyle(EditorStyles.label)
            {
                normal =
                {
                    textColor = new Color(0.937f, 0.937f, 0.937f),
                    background = MCGUI.TextureUtilities.CreateColoredTexture(new Color(Handles.xAxisColor.r*0.8f, Handles.xAxisColor.g*0.8f, Handles.xAxisColor.b*0.8f, 0.9f))
                },
                padding = new RectOffset(6, 1, 0, 0)
            };
            
            SceneAxisLabelY = new GUIStyle(EditorStyles.label)
            {
                normal =
                {
                    textColor = new Color(0.937f, 0.937f, 0.937f),
                    background = MCGUI.TextureUtilities.CreateColoredTexture(new Color(Handles.yAxisColor.r*0.8f, Handles.yAxisColor.g*0.8f, Handles.yAxisColor.b*0.8f, 0.9f))
                },
                padding = new RectOffset(6, 1, 0, 0)
            };
            
            SceneAxisLabelZ = new GUIStyle(EditorStyles.label)
            {
                normal =
                {
                    textColor = new Color(0.937f, 0.937f, 0.937f),
                    background = MCGUI.TextureUtilities.CreateColoredTexture(new Color(Handles.zAxisColor.r*0.8f, Handles.zAxisColor.g*0.8f, Handles.zAxisColor.b*0.8f, 0.9f))
                },
                padding = new RectOffset(6, 1, 0, 0)
            };
        }
    }
}

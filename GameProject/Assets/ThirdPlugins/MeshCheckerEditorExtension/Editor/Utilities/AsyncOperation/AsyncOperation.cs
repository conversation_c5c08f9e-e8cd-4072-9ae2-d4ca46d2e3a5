using System;
using System.Threading;
using JetBrains.Annotations;
using UnityEngine;

namespace HightlanderSolutions.MeshCheckerEditorExtension.Async
{
    /// <summary>
    ///     Represents an asynchronous operation.
    /// </summary>
    /// <typeparam name="T">The type of a result.</typeparam>
    public class AsyncOperation<T>
    {
        /// <summary>
        ///     Occurs when the async operation ended.
        /// </summary>
        public event Action<T, AsyncOperationResultState> OperationEndedCallback;

        /// <summary>
        ///     Gets a value indicating whether the async operaton should be aborted.
        /// </summary>
        public bool Aborted { get; private set; }

        /// <summary>
        ///     Gets a value indicating whether the async operation are ended.
        /// </summary>
        public bool IsEnded { get; private set; }

        /// <summary>
        ///     Gets the result of async operation.
        /// </summary>
        public T Result { get; private set; }

        #region Progress

        /// <summary>
        ///     The backing field of the <see cref="CurrentProgress"/> property.
        /// </summary>
        private int InnerCurrentProgress;

        /// <summary>
        ///     Gets a current progress of the async operation.
        /// </summary>
        /// <remarks>
        ///     Use <see cref="IncrementProgress"/> to update the value.
        /// </remarks>
        public int CurrentProgress
        {
            get { return InnerCurrentProgress; }
            private set { InnerCurrentProgress = value; }
        }

        /// <summary>
        ///     Gets the progress limit.
        /// </summary>
        public int TargetProgress { get; private set; }

        /// <summary>
        ///     Gets a progress state which value is between <c>0f</c> and <c>1f</c>, where <c>1f</c> is «ended».
        /// </summary>
        public float Progress
        {
            get
            {
                return Mathf.Clamp01((float)CurrentProgress / TargetProgress);
            }
        }

        /// <summary>
        ///     Increments the <see cref="CurrentProgress"/> counter. This operation is thread-safe.
        /// </summary>
        public void IncrementProgress()
        {
            try
            {
                Interlocked.Increment(ref InnerCurrentProgress);
            }
            catch (NullReferenceException NullReferenceException)
            {
                Debug.LogException(NullReferenceException);
            }
        }

        #endregion

        /// <summary>
        ///     Initializes a new instance of the <see cref="AsyncOperation{T}"/> class.
        /// </summary>
        /// <param name="targetProgress">The target progress.</param>
        public AsyncOperation(int targetProgress)
        {
            CurrentProgress = 0;
            TargetProgress = targetProgress;

            IsEnded = false;
            Aborted = false;
            Result = default(T);
        }

        /// <summary>
        ///     Initializes a new instance of the <see cref="AsyncOperation{T}"/> class.
        /// </summary>
        /// <param name="targetProgress">The target progress.</param>
        /// <param name="callback">The callback.</param>
        public AsyncOperation(int targetProgress, [CanBeNull]Action<T, AsyncOperationResultState> callback)
        {
            CurrentProgress = 0;
            TargetProgress = targetProgress;

            IsEnded = false;
            Aborted = false;
            Result = default(T);

            OperationEndedCallback = callback;
        }

        /// <summary>
        /// Abort async operation
        /// </summary>
        public void Abort()
        {
            Aborted = true;
        }

        /// <summary>
        ///     Ends asynchronous operation.
        /// </summary>
        /// <param name="resultState">Specifies operation end state.</param>
        /// <param name="result">The async operation result.</param>
        public void End(AsyncOperationResultState resultState, T result)
        {
            Result = result;

            IsEnded = true;
            CurrentProgress = TargetProgress;

            if (OperationEndedCallback != null)
            {
                try
                {
                    OperationEndedCallback(result, resultState);
                }
                catch (Exception E)
                {
                    Debug.LogException(E);
                }
            }
        }

        /// <summary>
        ///     Sets a target progress of the async operation (target progress will be clamped [1, int.MaxValue])
        /// </summary>
        /// <param name="targetProgress">A progress limit of the async operation.</param>
        public void SetTargetProgress(int targetProgress)
        {
            TargetProgress = Mathf.Clamp(targetProgress, 1, int.MaxValue);
        }
    }
}
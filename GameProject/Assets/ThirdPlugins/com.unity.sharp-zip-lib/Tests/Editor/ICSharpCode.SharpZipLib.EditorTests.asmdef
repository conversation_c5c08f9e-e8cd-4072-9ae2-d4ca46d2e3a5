{"name": "ICSharpCode.SharpZipLib.EditorTests", "references": ["UnityEngine.TestRunner", "UnityEditor.TestRunner", "Unity.SharpZipLib.Utils"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": ["nunit.framework.dll"], "autoReferenced": false, "defineConstraints": ["UNITY_INCLUDE_TESTS"], "versionDefines": [], "noEngineReferences": false}
using System.Reflection;
using System.Text;
using HutongGames.PlayMaker;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Camera)]
    [Tooltip("摄像机：拖拽设置")]
    public class CameraDrag : LuaFsmStateAction
    {
        [Tooltip("是否可以拖拽摄像机\n【勾】可以拖拽摄像机\n【空】不可拖拽摄像机")]
        public FsmBool draggable;
        [RequiredField] 
        [Tooltip("在当前State执行完毕后，是否还原此Action的设置\n【勾】还原设置\n【空】不还原设置")]
        public FsmBool autoRecovery;
    }
}
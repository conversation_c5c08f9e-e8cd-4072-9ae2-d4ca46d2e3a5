using HutongGames.PlayMaker;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("引导：拼接旋转指引")]
    public class UIMontageRotateGuide : LuaFsmStateAction
    {
        [Tooltip("旋转至角度")]
        public FsmFloat toRotate = 90.0f;

        [Tooltip("旋转总时间")]
        public FsmFloat time = 1.5f;

        [Tooltip("滑动表现的时长(指引快慢)")]
        public FsmFloat duration = 0.75f;

        [Tooltip("从右往左表现")]
        public FsmBool isRightToLeft = false;

        [<PERSON>lt<PERSON>("气泡文本")]
        public FsmString textBubble = "";
    }
}

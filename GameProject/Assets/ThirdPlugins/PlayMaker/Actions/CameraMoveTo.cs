using HutongGames.PlayMaker;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Camera)]
    [Tooltip("摄像机：移动至对象或位置")]
    public class CameraMoveTo : LuaFsmStateAction
    {
        [Control]
        [Tooltip("移动到位置或对象所在位置")]
        public FsmBool usePosition;
        
        [HideIf("HideObectName")]
        [Tooltip("设置移动至对象的名称")]
        public FsmString targetObjectName;
        [HideIf("HidePosition")]
        [Tooltip("设置移动至的位置")]
        public FsmVector3 targetPosition;
        
        [Control]
        [Tooltip("调整摄像机距离")]
        public FsmBool changedDistance;
        
        [HideIf("HideDistance")]
        [Tooltip("设置目标距离")]
        public FsmFloat targetDistance;
        
        [RequiredField] 
        [Tooltip("移动到新的对象耗费的时长(s)")]
        public FsmFloat time = 0.3f;
        
        public bool HideObectName() => usePosition.Value;
        public bool HidePosition() => !usePosition.Value;
        public bool HideDistance() => !changedDistance.Value;
    }
}
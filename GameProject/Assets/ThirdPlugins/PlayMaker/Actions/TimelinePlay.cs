using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Effects)]
    [Tooltip("TimeLine：播放")]
    public class TimelinePlay : LuaFsmStateAction
    {
        [Serializable]
        public class FsmTimelineTrack
        {
            public FsmString trackName;
            public FsmString gameObjectName;
        }
        
        [RequiredField] 
        [Tooltip("设置想要播放的TimeLine路径")]
        public FsmString timelinePath;
        
        [Control]
        [Tooltip("是否使用内城位置")]
        public FsmBool useInnerCityCoordinate;
        
        [HideIf("HidePosition")]
        [Tooltip("播放TimeLine的坐标")]
        public FsmVector3 position;
        
        [HideIf("HideCoordinate")]
        [Tooltip("播放Timeline内城高度")]
        public FsmFloat height;
        
        [HideIf("HideCoordinate")]
        [Tooltip("播放TimeLine的内城坐标")]
        public FsmVector2 coordinate;
        
        // [Control]
        // public FsmBool overrideRotation;
        // [HideIf("HideRotation")] 
        // public FsmVector3 rotation;
        
        [Tooltip("是否在播放TimeLine时显示UI\n【勾】显示UI\n【空】不显示UI")]
        public FsmBool uiShow;

        // [Control]
        // [Tooltip("是否将Player对象置入到Timeline中\n【勾】置入Player\n【空】不置入Player")]
        // public FsmBool usePlayer;
        //
        // [HideIf("HidePlayer")]
        // [Tooltip("Player对象的名称")]
        // public FsmString playerName;
        
        [Control]
        [Tooltip("是否替换timeline中的角色对象")]
        public FsmBool replaceObject;
        
        [HideIf("HideTimelineObjects")]
        [Tooltip("角色置入")]
        public FsmTimelineTrack[] timelineTracks = new FsmTimelineTrack[1];

        [Control] 
        [Tooltip("是否播放音效")] 
        public FsmBool playSound = false;
        
        [Tooltip("背景音乐名称")]
        [HideIf("HideSound")]
        public FsmString soundName;
        
        [Tooltip("是否在播放完Timeline后删除\n【勾】删除Timeline\n【空】不删除Timeline")]
        public FsmBool autoRemove = true;
        
        [Control]
        [Tooltip("创建出来的对象是否替换场景中的某个对象")]
        public FsmBool autoReplace;

        [HideIf("HideReplaceGameObject")]
        [Tooltip("被替换场景中的对象名称")]
        public FsmString replaceObjectName;

        // public bool HidePlayer() => !usePlayer.Value;
        public bool HideTimelineObjects() => !replaceObject.Value;
        public bool HideSound() => !playSound.Value;
        public bool HideReplaceGameObject() => !autoReplace.Value;
        public bool HidePosition() => useInnerCityCoordinate.Value;
        public bool HideCoordinate() => !useInnerCityCoordinate.Value;

        protected override string ToLua(object v)
        {
            if (v is FsmTimelineTrack[])
            {
                FsmTimelineTrack[] vArray = (FsmTimelineTrack[]) v;
                
                var index = 0;
                StringBuilder sb = new StringBuilder();
                foreach (var ss in vArray)
                {
                    if (index > 0)
                        sb.Append(", ");
                    index++;
                    sb.AppendFormat("{{ {0} = \"{1}\", {2} = \"{3}\" }}", nameof(ss.trackName), ss.trackName, nameof(ss.gameObjectName), ss.gameObjectName);
                }
                return string.Format("{{ {0} }}", sb);
            }
            return string.Empty;
        }
    }
}
using HutongGames.PlayMaker;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Camera)]
    [Tooltip("摄像机：追踪开启或关闭")]
    public class CameraFollow : LuaFsmStateAction
    {
        [Control]
        [Tooltip("是否切换跟踪对象")]
        public FsmBool changeTarget;
        
        [Toolt<PERSON>("跟踪对象名称")]
        [HideIf("HideGameObjectName")]
        public FsmString gameObjectName;
        
        [Tooltip("是否暂停摄像机自动跟踪对象\n【勾】开启跟踪\n【空】关闭跟踪")]
        public FsmBool follow;
        
        [Tooltip("摄像机是否瞬间对焦到对象上\n【勾】是\n【空】否")]
        public FsmBool immediately;
        
        [Tooltip("摄像机距离设置为默认")]
        public FsmBool resetDistance = false;
        
        [Control]
        [Tooltip("摄像机距离使用默认值")]
        [HideIf("HideResetDistance")]
        public FsmBool customDistance = true;
        
        [Tooltip("摄像机距离设置")]
        [HideIf("HideUseCustomDistance")]
        public FsmFloat distance = 35.35535f;
        
        [Tooltip("在当前State执行完毕后，是否还原此Action的设置\n【勾】还原设置\n【空】不还原设置")]
        public FsmBool autoRecovery;

        public bool HideGameObjectName() => !changeTarget.Value;
        public bool HideResetDistance() => !resetDistance.Value;
        public bool HideUseCustomDistance() => !resetDistance.Value || !customDistance.Value;
    }
}
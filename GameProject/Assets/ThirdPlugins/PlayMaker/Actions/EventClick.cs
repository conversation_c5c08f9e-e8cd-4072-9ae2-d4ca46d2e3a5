using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.Input)]
    [Tooltip("点击3D物体")]
    public class EventClick : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("3D物体名称")]
        public FsmString gameObjectName;

        [Toolt<PERSON>("是否显示手指")]
        public FsmBool useFinger = true;
        
        [HideIf("HideOffset")]
        [Tooltip("相对主体Object的偏移值")]
        public FsmVector3 offset;
        
        [HideIf("HideScale")]
        [Tooltip("手指缩放比例")]
        public FsmVector3 scale = Vector3.one;

        [Control] 
        [Tooltip("距离太远是否开启寻路")]
        public FsmBool usePathFinding;

        [HideIf("HideUsePathFinding")]
        [Tooltip("玩家角色名称")]
        public FsmString characterName;
        
        [HasFloatSlider(0.1f, 5f)]
        [HideIf("HideUsePathFinding")]
        [Tooltip("结束距离")]
        public FsmFloat finishDistance;
        
        [Control]
        [Tooltip("是否显示点击效果")]
        public FsmBool showEffect;
        
        [HideIf("HideEffect")] 
        [Tooltip("点击特效位置")]
        public FsmVector3 effectPosition;

        public bool HideEffect() => !showEffect.Value;
        public bool HideUsePathFinding() => !usePathFinding.Value;
        public bool HideOffset() => !useFinger.Value;
        public bool HideScale() => !useFinger.Value;
    }
}
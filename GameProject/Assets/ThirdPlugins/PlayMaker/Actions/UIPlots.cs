using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    public class UIPlots : LuaFsmStateAction
    {
        [Serializable]
        public class FsmPlot
        {
            [RequiredField]
            [Tooltip("跳过按钮是否可见")]
            public FsmString content;

            [Tooltip("文本入场动画时间")]
            public FsmFloat fadeInTime = 0.5f;

            [Tooltip("文本出场动画时间")]
            public FsmFloat fadeOutTime = 0.5f;
            
            [Tooltip("文本停留时间")]
            public FsmFloat stayTime = 2f;
        }
        
        [Tooltip("跳过按钮是否可见")]
        public FsmBool skipButtonVisible = false;

        [Tooltip("入场动画时间")]
        public FsmFloat fadeInTime = 0.5f;

        [Tooltip("出场动画时间")]
        public FsmFloat fadeOutTime = 0.5f;

        [Toolt<PERSON>("内容间隔时间")]
        public FsmFloat intervalTime = 0.5f;

        [Toolt<PERSON>("内容配置")]
        public FsmPlot[] plots;

        protected override string ToLua(object v)
        {
            if (v is FsmPlot[])
            {
                FsmPlot[] vArray = (FsmPlot[]) v;
                
                StringBuilder sb = new StringBuilder();
            
                var index = 0;
                foreach (var fsmVar in vArray)
                {
                    if (index > 0)
                        sb.Append(", ");
                    index++;
                    
                    sb.AppendFormat("{{ {0} = {1}, ", nameof(fsmVar.content), ToLua(fsmVar.content));
                    sb.AppendFormat("{0} = {1}, ", nameof(fsmVar.fadeInTime), ToLua(fsmVar.fadeInTime));
                    sb.AppendFormat("{0} = {1}, ", nameof(fsmVar.fadeOutTime), ToLua(fsmVar.fadeOutTime));
                    sb.AppendFormat("{0} = {1} }}", nameof(fsmVar.stayTime), ToLua(fsmVar.stayTime));
                }
                return string.Format("{{ {0} }}", sb);
            }
            return v.ToString();
        }
    }
}
using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    [Tooltip("显示一个UI页面")]
    public class UIShow : LuaFsmStateAction
    {
        [Serializable]
        public enum ParamType
        {
            None,
            RawParam,
            LUA
        }

        [RequiredField] 
        [Tooltip("UI页面名称")]
        public FsmString uiName;

        // [Control]
        [Tooltip("是否需要参数")]
        public ParamType paramType;
        
        [HideIf("HideUIParam")]
        [Tooltip("UI页面参数")]
        public FsmSValue[] uiParam;

        [HideIf("HideLua")]
        [Tooltip("LUA文件名称")]
        public FsmString fileName;
        
        [HideIf("HideLua")]
        [Tooltip("配置ID")]
        public FsmString configID;

        public bool HideUIParam() => paramType != ParamType.RawParam;
        public bool HideLua() => paramType != ParamType.LUA;
    }
}
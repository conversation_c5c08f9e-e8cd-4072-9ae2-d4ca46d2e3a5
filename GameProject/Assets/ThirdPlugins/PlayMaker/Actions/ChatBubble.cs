using System.Collections;
using System.Collections.Generic;
using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMaker
{
    [ActionCategory(ActionCategory.UI)]
    public class ChatBubble : LuaFsmStateAction
    {
        [RequiredField] 
        [Tooltip("添加对话的角色")]
        public FsmString gameObjectName;
        
        [RequiredField] 
        [Tooltip("对话偏移高度")]
        public FsmFloat height;
        
        [RequiredField] 
        [Tooltip("对话内容")]
        public FsmString text;
        
        [RequiredField] 
        [Tooltip("对话显示时间")]
        public FsmFloat time = 0.2f;
        
        [RequiredField] 
        [Tooltip("等待泡泡对话结束")]
        public FsmBool waitForFinish = false;
    }
}
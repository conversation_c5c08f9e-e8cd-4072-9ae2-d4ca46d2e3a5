// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.GlobalsAsset
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using System.ComponentModel;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public static class GlobalsAsset
  {
    private const string copiedGlobalsFilename = "Assets/PlaymakerGlobals_EXPORTED.asset";
    private const string exportedUnitypackage = "PlayMakerGlobals.unitypackage";
    private static PlayMakerGlobals projectGlobals;

    [Localizable(false)]
    public static void Export()
    {
      AssetDatabase.Refresh();
      FsmEditor.SaveGlobals();
      AssetDatabase.CopyAsset(AssetDatabase.GetAssetPath((UnityEngine.Object) PlayMakerGlobals.Instance), "Assets/PlaymakerGlobals_EXPORTED.asset");
      AssetDatabase.Refresh();
      string fileName = EditorUtility.SaveFilePanel(Strings.Dialog_Export_Globals, "", "PlayMakerGlobals.unitypackage", "unitypackage");
      if (fileName.Length == 0)
        return;
      AssetDatabase.ExportPackage("Assets/PlaymakerGlobals_EXPORTED.asset", fileName);
      AssetDatabase.DeleteAsset("Assets/PlaymakerGlobals_EXPORTED.asset");
      AssetDatabase.Refresh();
      Dialogs.OkDialog(Strings.Labels_Use_Import_Globals_);
    }

    [Localizable(false)]
    public static void Import()
    {
      AssetDatabase.Refresh();
      GlobalsAsset.projectGlobals = PlayMakerGlobals.Instance;
      if (EditorUtility.OpenFilePanel(Strings.Dialog_Import_Globals, "", "unitypackage").Length == 0)
        return;
      AssetDatabase.ImportPackage("PlayMakerGlobals.unitypackage", false);
      EditorApplication.update -= new EditorApplication.CallbackFunction(GlobalsAsset.DoImport);
      EditorApplication.update += new EditorApplication.CallbackFunction(GlobalsAsset.DoImport);
    }

    private static void DoImport()
    {
      EditorApplication.update -= new EditorApplication.CallbackFunction(GlobalsAsset.DoImport);
      Debug.Log((object) Strings.Label_Importing_Globals_);
      PlayMakerGlobals[] objectsOfTypeAll = (PlayMakerGlobals[]) Resources.FindObjectsOfTypeAll(typeof (PlayMakerGlobals));
      if (objectsOfTypeAll.Length == 1)
      {
        Dialogs.OkDialog(Strings.Dialog_No_Globals_to_import);
      }
      else
      {
        string str = "";
        foreach (PlayMakerGlobals source in objectsOfTypeAll)
        {
          if ((UnityEngine.Object) source != (UnityEngine.Object) GlobalsAsset.projectGlobals)
          {
            Debug.Log((object) (Strings.Label_Importing_ + AssetDatabase.GetAssetPath((UnityEngine.Object) source)));
            str += FsmBuilder.MergeGlobals(source, GlobalsAsset.projectGlobals);
          }
        }
        if (!string.IsNullOrEmpty(str))
          Dialogs.OkDialog(Strings.Dialog_ImportGlobals_Error + Environment.NewLine + str);
        foreach (PlayMakerGlobals playMakerGlobals in objectsOfTypeAll)
        {
          if ((UnityEngine.Object) playMakerGlobals != (UnityEngine.Object) GlobalsAsset.projectGlobals)
            AssetDatabase.DeleteAsset(AssetDatabase.GetAssetPath((UnityEngine.Object) playMakerGlobals));
        }
        FsmEditor.SaveGlobals();
      }
    }
  }
}

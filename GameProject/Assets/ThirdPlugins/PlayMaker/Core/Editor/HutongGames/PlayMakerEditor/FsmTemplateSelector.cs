// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmTemplateSelector
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class FsmTemplateSelector : BaseEditorWindow
  {
    private static FsmTemplateSelector instance;
    private SearchBox searchBox;
    private string searchString = "";
    private Vector2 scrollPosition;
    private FsmTemplate selectedTemplate;
    private FsmTemplate prevTemplate;
    private FsmTemplate beforeSelected;
    private FsmTemplate afterSelected;
    private string selectedTemplateCategory;
    private float scrollViewHeight;
    private float descriptionHeight;
    private Rect selectedRect;
    private bool autoScroll;
    private Vector2 mousePos;
    private FsmTemplate mouseOverTemplate;
    private FsmTemplate mouseDownTemplate;
    private string mouseOverCategory;
    private int selectedCategory = -1;
    private string selectedCategoryName;
    private List<FsmVariable> inputVariables = new List<FsmVariable>();
    private List<FsmVariable> outputVariables = new List<FsmVariable>();
    private float previewScrollViewHeight;
    private Vector2 previewScrollPosition;
    private float previewTemplateGUIHeight;
    private readonly List<FsmTemplate> filteredTemplates = new List<FsmTemplate>();
    private readonly List<string> filteredCategoryLookup = new List<string>();
    private readonly List<string> filteredCategories = new List<string>();
    private readonly List<int> filteredCategoryIDs = new List<int>();
    private static FsmTemplate delayedSelectTemplate;
    private readonly GUIContent countLabel = new GUIContent();

    public static void Refresh()
    {
      if ((UnityEngine.Object) FsmTemplateSelector.instance == (UnityEngine.Object) null)
        return;
      FsmTemplateSelector.instance.ResetView();
    }

    public static void FindTemplateInBrowser(FsmTemplate template)
    {
      EditorApplication.ExecuteMenuItem("PlayMaker/Editor Windows/Templates Browser");
      FsmTemplateSelector.Refresh();
      FsmTemplateSelector.delayedSelectTemplate = template;
      EditorApplication.update += new EditorApplication.CallbackFunction(FsmTemplateSelector.DelayedSelectTemplate);
    }

    private static void DelayedSelectTemplate()
    {
      if ((UnityEngine.Object) FsmTemplateSelector.instance == (UnityEngine.Object) null)
        return;
      FsmTemplateSelector.instance.SelectTemplate(FsmTemplateSelector.delayedSelectTemplate);
      EditorApplication.update -= new EditorApplication.CallbackFunction(FsmTemplateSelector.DelayedSelectTemplate);
    }

    public override void Initialize()
    {
      if ((UnityEngine.Object) FsmTemplateSelector.instance == (UnityEngine.Object) null)
        FsmTemplateSelector.instance = this;
      this.isToolWindow = true;
      this.minSize = new Vector2(200f, 250f);
      this.wantsMouseMove = true;
      this.InitSearchBox();
      this.SelectTemplate((FsmTemplate) null);
      this.BuildFilteredList();
      this.OpenCategory(FsmEditorSettings.SelectedTemplateCategory);
      Undo.undoRedoPerformed += new Undo.UndoRedoCallback(this.UndoRedoPerformed);
      FsmEditor.OnFsmControlsChanged += new Action<Fsm>(this.RebuildControls);
    }

    public void OnFocus() => this.mouseDownTemplate = (FsmTemplate) null;

    public override void InitWindowTitle() => this.SetTitle(Strings.TemplateSelector_Title);

    private void InitSearchBox()
    {
      if (this.searchBox != null)
        return;
      this.searchBox = new SearchBox((EditorWindow) this)
      {
        SearchModes = new string[2]{ "Name", "Description" },
        HasPopupSearchModes = true
      };
      this.searchBox.SearchChanged += new EditorApplication.CallbackFunction(this.UpdateSearchResults);
      this.searchBox.Focus();
    }

    private void OnDisable()
    {
      if ((UnityEngine.Object) FsmTemplateSelector.instance == (UnityEngine.Object) this)
        FsmTemplateSelector.instance = (FsmTemplateSelector) null;
      Undo.undoRedoPerformed -= new Undo.UndoRedoCallback(this.UndoRedoPerformed);
      FsmEditor.OnFsmControlsChanged -= new Action<Fsm>(this.RebuildControls);
    }

    public override void DoGUI()
    {
      this.HandleKeyboardInput();
      this.DoToolbar();
      this.DoHints();
      this.DoTemplateList();
      this.DoBottomPanel();
      this.HandleDragAndDrop();
    }

    private void HandleDragAndDrop()
    {
      if (this.eventType != UnityEngine.EventType.DragUpdated && this.eventType != UnityEngine.EventType.DragPerform)
        return;
      DragAndDrop.visualMode = DragAndDropVisualMode.Move;
      if (this.mouseOverCategory != null && this.mouseOverCategory != this.selectedTemplateCategory && this.eventType == UnityEngine.EventType.DragPerform)
      {
        Templates.SetCategory(this.selectedTemplate, this.mouseOverCategory);
        this.SelectTemplate(this.selectedTemplate);
        this.ResetView();
        GUIHelpers.SafeExitGUI();
      }
      if (this.eventType == UnityEngine.EventType.DragPerform)
        this.ResetDragAndDrop();
      Event.current.Use();
    }

    private void ResetDragAndDrop()
    {
      DragAndDropManager.Reset();
      this.mouseDownTemplate = (FsmTemplate) null;
    }

    private void DoHints()
    {
      if (!FsmEditorSettings.ShowHints)
        return;
      GUILayout.Box(Strings.Hint_Template_Selector, FsmEditorStyles.HintBox);
    }

    private void DoToolbar()
    {
      GUILayout.BeginHorizontal(EditorStyles.toolbar);
      GUILayout.Space(4f);
      this.searchBox.OnGUI();
      GUILayout.Space(10f);
      GUILayout.EndHorizontal();
    }

    private void FixCategorySelection()
    {
      if ((UnityEngine.Object) this.selectedTemplate == (UnityEngine.Object) null || this.selectedTemplate.Category == this.selectedCategoryName)
        return;
      if (string.IsNullOrEmpty(this.selectedTemplate.Category))
        this.selectedCategory = 0;
      else
        this.selectedCategory = Templates.Categories.IndexOf(this.selectedTemplate.Category);
    }

    private void DoTemplateList()
    {
      if (this.isRepaint)
        this.mouseOverCategory = (string) null;
      this.scrollPosition = GUILayout.BeginScrollView(this.scrollPosition, false, false);
      this.mouseOverTemplate = (FsmTemplate) null;
      if (Event.current.isMouse)
        this.mousePos = Event.current.mousePosition;
      this.FixCategorySelection();
      if (string.IsNullOrEmpty(this.searchString))
        this.DoFullTemplateList();
      else
        this.DoFilteredTemplateList();
      if (GUIHelpers.ClickableEmptySpace())
        this.Deselect();
      HighlighterHelper.EndScrollView("Templates List");
      this.DoAutoScroll();
    }

    private void DoFilteredTemplateList()
    {
      foreach (int filteredCategoryId in this.filteredCategoryIDs)
      {
        GUILayout.BeginVertical();
        this.DoCategoryButton(filteredCategoryId);
        string category = Templates.Categories[filteredCategoryId];
        for (int index = 0; index < this.filteredTemplates.Count; ++index)
        {
          if (this.filteredCategoryLookup[index] == category)
            this.DoTemplateGUI(this.filteredTemplates[index]);
        }
        GUILayout.EndVertical();
        if (this.isRepaint && GUILayoutUtility.GetLastRect().Contains(Event.current.mousePosition))
          this.mouseOverCategory = category;
      }
    }

    private void DoFullTemplateList()
    {
      foreach (int filteredCategoryId in this.filteredCategoryIDs)
      {
        if (this.DoCategoryButton(filteredCategoryId))
          this.OpenCategory(filteredCategoryId);
        else
          this.CloseCategory(filteredCategoryId);
        if (this.selectedCategory == filteredCategoryId)
          this.DoTemplateCategory(this.selectedCategoryName);
      }
    }

    private bool DoCategoryButton(int categoryIndex)
    {
      string category = Templates.Categories[categoryIndex];
      Texture categoryIcon = Actions.GetCategoryIcon(category);
      GUIContent content = FsmEditorContent.TempContent(category);
      Rect rect1 = GUILayoutUtility.GetRect(content, FsmEditorStyles.ActionCategory);
      int num = GUI.Toggle(rect1, FsmEditorSettings.SelectedTemplateCategory == categoryIndex, content, FsmEditorStyles.ActionCategory) ? 1 : 0;
      if (!this.isRepaint)
        return num != 0;
      Rect rect2 = new Rect(rect1);
      rect2.x += 6f;
      rect2.y += 3f;
      rect2.width = rect2.height = 16f;
      GUIStyle.none.Draw(rect2, categoryIcon);
      int templatesInCategory = Templates.GetNumTemplatesInCategory(category);
      if (templatesInCategory > 0)
        FsmEditorStyles.ActionCategoryCount.Draw(rect1, "[" + (object) templatesInCategory + "]");
      if (!GUILayoutUtility.GetLastRect().Contains(Event.current.mousePosition))
        return num != 0;
      this.mouseOverCategory = category;
      return num != 0;
    }

    private void OpenCategory(int i, bool force = false)
    {
      if (this.selectedCategory == i && !force)
        return;
      this.selectedCategory = i;
      this.selectedCategoryName = Templates.Categories[i];
      FsmEditorSettings.SelectedTemplateCategory = i;
      FsmEditorSettings.SaveSettings();
      this.Repaint();
    }

    private void CloseCategory(int i)
    {
      if (FsmEditorSettings.SelectedTemplateCategory != i)
        return;
      this.selectedCategory = -1;
      this.selectedCategoryName = "";
      this.selectedTemplate = (FsmTemplate) null;
      FsmEditorSettings.SelectedTemplateCategory = -1;
      FsmEditorSettings.SaveSettings();
      this.Repaint();
    }

    private void DoTemplateCategory(string category)
    {
      if (string.IsNullOrEmpty(category))
        return;
      foreach (FsmTemplate template in Templates.GetTemplatesInCategory(category))
        this.DoTemplateGUI(template);
    }

    private void DoTemplateGUI(FsmTemplate template)
    {
      if ((UnityEngine.Object) template == (UnityEngine.Object) null)
        return;
      int num = (UnityEngine.Object) template == (UnityEngine.Object) this.selectedTemplate ? 1 : 0;
      GUIStyle style1 = num != 0 ? FsmEditorStyles.ActionItemSelected : FsmEditorStyles.ActionItem;
      GUIStyle style2 = num != 0 ? FsmEditorStyles.ActionLabelSelected : FsmEditorStyles.ActionLabel;
      GUILayout.BeginHorizontal(style1);
      int count = FsmSearch.GetTemplateAllUsageList(template).Count;
      string text = count > 0 ? string.Format(Strings.ActionSelector_Count_Postfix, (object) count) : "";
      Rect position = this.position;
      float maxWidth = position.width - 42f;
      if (count > 0)
      {
        this.countLabel.text = text;
        maxWidth -= style2.CalcSize(this.countLabel).x + 3f;
      }
      GUILayout.Label(template.name, style2, GUILayout.MaxWidth(maxWidth));
      if (count > 0)
      {
        GUILayout.FlexibleSpace();
        GUILayout.Label(text, style2);
      }
      GUILayout.EndHorizontal();
      Rect lastRect = GUILayoutUtility.GetLastRect();
      if ((double) this.mousePos.y > (double) this.scrollPosition.y && (double) this.mousePos.y < (double) this.scrollPosition.y + (double) this.scrollViewHeight && lastRect.Contains(this.mousePos))
        this.mouseOverTemplate = template;
      if ((UnityEngine.Object) this.mouseOverTemplate == (UnityEngine.Object) template)
      {
        if (this.eventType == UnityEngine.EventType.MouseDown)
        {
          this.mouseDownTemplate = template;
          if (Event.current.button == 1 || EditorGUI.actionKey)
            this.TemplateContextMenu(template);
        }
        if (this.eventType == UnityEngine.EventType.MouseUp)
        {
          if ((UnityEngine.Object) template == (UnityEngine.Object) this.mouseDownTemplate)
          {
            this.ResetDragAndDrop();
            this.SelectTemplate(template);
            GUIUtility.ExitGUI();
          }
          this.ResetDragAndDrop();
          return;
        }
        if (this.eventType == UnityEngine.EventType.MouseDrag && (UnityEngine.Object) this.mouseDownTemplate != (UnityEngine.Object) null)
        {
          DragAndDropManager.StartAddTemplate(this.mouseDownTemplate);
          return;
        }
      }
      if (this.isRepaint && template.fsm.HasErrors)
      {
        position = new Rect(GUILayoutUtility.GetLastRect());
        position.width = 14f;
        position.height = 14f;
        GUI.DrawTexture(position, (Texture) FsmEditorStyles.Errors);
      }
      if ((UnityEngine.Object) template == (UnityEngine.Object) this.selectedTemplate)
      {
        this.beforeSelected = this.prevTemplate;
        if (this.isRepaint)
        {
          this.selectedRect = GUILayoutUtility.GetLastRect();
          this.selectedRect.y -= this.scrollPosition.y;
        }
      }
      if ((UnityEngine.Object) this.prevTemplate == (UnityEngine.Object) this.selectedTemplate)
        this.afterSelected = template;
      this.prevTemplate = template;
    }

    private void SelectTemplate(FsmTemplate template)
    {
      this.autoScroll = true;
      this.Repaint();
      this.selectedTemplate = template;
      if (!((UnityEngine.Object) this.selectedTemplate != (UnityEngine.Object) null))
        return;
      this.selectedTemplateCategory = Templates.GetCategory(this.selectedTemplate);
      this.OpenCategory(Templates.GetCategoryIndex(this.selectedTemplateCategory));
      this.selectedTemplate = template;
      if (!this.searchBox.HasFocus)
        Keyboard.ResetFocus();
      if (!FsmEditorSettings.LockGraphView)
        FsmEditor.SelectFsm(template.fsm);
      this.BuildControls();
    }

    private void RebuildControls(Fsm fsm)
    {
      if (fsm.OwnerObject != (UnityEngine.Object) this.selectedTemplate)
        return;
      this.BuildControls();
      this.Repaint();
    }

    private void BuildControls()
    {
      List<FsmVariable> fsmVariableList = FsmVariable.GetFsmVariableList((UnityEngine.Object) this.selectedTemplate);
      this.inputVariables = fsmVariableList.Where<FsmVariable>((Func<FsmVariable, bool>) (x => x.ShowInInspector)).ToList<FsmVariable>();
      this.outputVariables = fsmVariableList.Where<FsmVariable>((Func<FsmVariable, bool>) (x => x.IsOutput)).ToList<FsmVariable>();
    }

    private void ResetView()
    {
      Templates.InitList();
      this.BuildFilteredList();
      if ((UnityEngine.Object) this.selectedTemplate != (UnityEngine.Object) null)
        this.SelectTemplate(this.selectedTemplate);
      else
        this.OpenCategory(this.selectedCategory, true);
      this.ResetDragAndDrop();
      this.Repaint();
    }

    private void UndoRedoPerformed()
    {
      this.searchBox.Focus();
      Templates.InitList();
      this.ResetView();
    }

    private void SelectPrevious()
    {
      if ((UnityEngine.Object) this.selectedTemplate == (UnityEngine.Object) null || (UnityEngine.Object) this.beforeSelected == (UnityEngine.Object) null)
        return;
      this.SelectTemplate(this.beforeSelected);
    }

    private void SelectNext()
    {
      if ((UnityEngine.Object) this.selectedTemplate == (UnityEngine.Object) null || (UnityEngine.Object) this.afterSelected == (UnityEngine.Object) null)
        return;
      this.SelectTemplate(this.afterSelected);
    }

    private void DoAutoScroll()
    {
      if (!this.isRepaint || !this.autoScroll)
        return;
      this.scrollViewHeight = GUILayoutUtility.GetLastRect().height;
      if ((double) this.selectedRect.y < 0.0)
      {
        this.scrollPosition.y += this.selectedRect.y;
        this.Repaint();
      }
      else if ((double) this.selectedRect.y + (double) this.selectedRect.height > (double) this.scrollViewHeight)
      {
        this.scrollPosition.y += this.selectedRect.y + this.selectedRect.height - this.scrollViewHeight;
        this.Repaint();
      }
      this.autoScroll = false;
    }

    private void Deselect()
    {
      this.selectedTemplate = (FsmTemplate) null;
      FsmEditor.Instance.OnSelectionChange();
      Keyboard.ResetFocus();
      this.Repaint();
      GUIHelpers.SafeExitGUI();
    }

    private void DoBottomPanel()
    {
      if ((UnityEngine.Object) this.selectedTemplate != (UnityEngine.Object) null)
      {
        GUILayout.BeginVertical(FsmEditorStyles.BottomBarBG);
        GUILayout.BeginVertical();
        GUILayout.Space(4f);
        GUILayout.BeginHorizontal();
        GUILayout.Label(this.selectedTemplate.name, FsmEditorStyles.ActionPreviewTitle, GUILayout.MaxWidth(this.position.width - 30f));
        GUILayout.FlexibleSpace();
        if (FsmEditorGUILayout.HelpButton())
        {
          if (!string.IsNullOrEmpty(this.selectedTemplate.fsm.DocUrl))
            Application.OpenURL(this.selectedTemplate.fsm.DocUrl);
          else
            EditorCommands.OpenWikiPage(WikiPages.TemplatesBrowser);
        }
        GUILayout.EndHorizontal();
        string text = this.selectedTemplate.fsm.Description;
        int num = !string.IsNullOrEmpty(text) ? 1 : 0;
        if (num == 0)
          text = Strings.Label_No_Description;
        EditorGUI.BeginDisabledGroup(num == 0);
        GUILayout.Box(text, FsmEditorStyles.LabelWithWordWrap);
        EditorGUI.EndDisabledGroup();
        if (this.isRepaint)
          this.descriptionHeight = GUILayoutUtility.GetLastRect().height;
        HighlighterHelper.EndVertical("Template Description");
        ActionEditor.PreviewMode = true;
        GUILayout.Space(5f);
        if (FsmEditorSettings.ShowTemplatePreview)
          this.DoTemplatePreview();
        ActionEditor.PreviewMode = false;
        GUILayout.EndVertical();
      }
      GUILayout.BeginHorizontal(FsmEditorStyles.ToolbarBottom);
      GUILayout.Space(6f);
      bool flag = GUILayout.Toggle(FsmEditorSettings.ShowTemplatePreview, Strings.ActionSelector_Preview, FsmEditorStyles.ToolbarCheckbox);
      if (flag != FsmEditorSettings.ShowTemplatePreview)
      {
        FsmEditorSettings.ShowTemplatePreview = flag;
        FsmEditorSettings.SaveSettings();
      }
      HighlighterHelper.FromGUILayout("Preview");
      GUILayout.FlexibleSpace();
      GUILayout.EndHorizontal();
    }

    private void DoTemplatePreview()
    {
      EditorGUIUtility.wideMode = true;
      FsmEditorGUILayout.AutoLabelWidth(this.position.width);
      int num1 = this.inputVariables != null ? this.inputVariables.Count : 0;
      int num2 = this.outputVariables != null ? this.outputVariables.Count : 0;
      bool flag = num1 + num2 > 0;
      if (flag)
        FsmEditorGUILayout.Divider();
      Rect rect = this.position;
      this.previewScrollViewHeight = Mathf.Min(rect.height - 150f - this.descriptionHeight, this.previewTemplateGUIHeight);
      this.previewScrollPosition = EditorGUILayout.BeginScrollView(this.previewScrollPosition, GUILayout.Height(this.previewScrollViewHeight));
      GUILayout.BeginVertical();
      EditorGUI.BeginDisabledGroup(true);
      if (num1 > 0)
      {
        GUILayout.Space(4f);
        GUILayout.Label(Strings.Label_Inputs, EditorStyles.boldLabel);
        FsmVariable.DoVariableListGUI(this.inputVariables);
      }
      if (num2 > 0)
      {
        GUILayout.Space(4f);
        GUILayout.Label(Strings.Label_Outputs, EditorStyles.boldLabel);
        FsmVariable.DoVariableListGUI(this.outputVariables);
      }
      EditorGUI.EndDisabledGroup();
      GUILayout.EndVertical();
      if (this.isRepaint)
      {
        float templateGuiHeight = this.previewTemplateGUIHeight;
        rect = GUILayoutUtility.GetLastRect();
        this.previewTemplateGUIHeight = rect.height + (flag ? 5f : 0.0f);
        if ((double) Math.Abs(this.previewTemplateGUIHeight - templateGuiHeight) > 1.0)
        {
          this.Repaint();
          this.autoScroll = true;
        }
      }
      HighlighterHelper.EndScrollView("Template Preview");
    }

    private void HandleKeyboardInput()
    {
      if (this.currentEvent.GetTypeForControl(GUIUtility.GetControlID(FocusType.Keyboard)) != UnityEngine.EventType.KeyDown)
        return;
      switch (this.currentEvent.keyCode)
      {
        case KeyCode.Escape:
          this.currentEvent.Use();
          this.Deselect();
          GUIUtility.ExitGUI();
          break;
        case KeyCode.UpArrow:
          this.currentEvent.Use();
          this.SelectPrevious();
          GUIUtility.ExitGUI();
          break;
        case KeyCode.DownArrow:
          this.currentEvent.Use();
          this.SelectNext();
          GUIUtility.ExitGUI();
          break;
      }
    }

    private void UpdateSearchResults()
    {
      this.searchString = this.searchBox.SearchString;
      if (string.IsNullOrEmpty(this.searchString))
        return;
      this.BuildFilteredList();
      this.SelectFirstMatchingTemplate();
    }

    [Localizable(false)]
    private void BuildFilteredList()
    {
      Templates.Init();
      this.filteredTemplates.Clear();
      this.filteredCategories.Clear();
      this.filteredCategoryLookup.Clear();
      this.filteredCategoryIDs.Clear();
      string[] strArray = this.searchString.ToUpper().Split(' ');
      foreach (FsmTemplate template in Templates.List)
      {
        if (!((UnityEngine.Object) template == (UnityEngine.Object) null) && (string.IsNullOrEmpty(this.searchString) || this.TemplateMatchesFilter(template, (IEnumerable<string>) strArray)) && template.name != null)
        {
          this.filteredTemplates.Add(template);
          string category = Templates.GetCategory(template);
          int categoryIndex = Templates.GetCategoryIndex(category);
          this.filteredCategoryLookup.Add(category);
          if (!this.filteredCategories.Contains(category))
            this.filteredCategories.Add(category);
          if (!this.filteredCategoryIDs.Contains(categoryIndex))
            this.filteredCategoryIDs.Add(categoryIndex);
        }
      }
    }

    private bool TemplateMatchesFilter(FsmTemplate template, IEnumerable<string> filter)
    {
      List<string> list = filter.ToList<string>();
      bool flag = FsmTemplateSelector.TemplateNameMatchesFilter(template, (IEnumerable<string>) list);
      return flag || this.searchBox.SearchMode == 0 ? flag : FsmTemplateSelector.TemplateDescriptionMatchesFilter(template, (IEnumerable<string>) list);
    }

    [Localizable(false)]
    private static bool TemplateNameMatchesFilter(FsmTemplate template, IEnumerable<string> filter)
    {
      if ((UnityEngine.Object) template == (UnityEngine.Object) null)
        return false;
      string str1 = template.name.ToUpper().Replace(" ", "");
      foreach (string str2 in filter)
      {
        if (!str1.Contains(str2))
          return false;
      }
      return true;
    }

    private static bool TemplateDescriptionMatchesFilter(
      FsmTemplate template,
      IEnumerable<string> filter)
    {
      string upper = template.fsm.Description.ToUpper();
      foreach (string str in filter)
      {
        if (!upper.Contains(str))
          return false;
      }
      return true;
    }

    private void SelectFirstMatchingTemplate()
    {
      if (this.filteredCategories.Count == 0)
        return;
      string filteredCategory = this.filteredCategories[0];
      for (int index = 0; index < this.filteredTemplates.Count; ++index)
      {
        if (this.filteredCategoryLookup[index] == filteredCategory)
        {
          this.SelectTemplate(this.filteredTemplates[index]);
          break;
        }
      }
    }

    private void TemplateContextMenu(FsmTemplate template)
    {
      GenericMenu genericMenu = Menus.TemplateUsageMenu(template, false);
      genericMenu.AddSeparator("");
      genericMenu.AddItem(new GUIContent(Strings.Command_Ping_Template_Asset), false, new GenericMenu.MenuFunction2(this.PingTemplate), (object) template);
      genericMenu.AddSeparator("");
      genericMenu.AddItem(new GUIContent(Strings.Command_Delete), false, new GenericMenu.MenuFunction2(this.DeleteTemplate), (object) template);
      genericMenu.ShowAsContext();
    }

    private void PingTemplate(object userdata)
    {
      FsmTemplate fsmTemplate = userdata as FsmTemplate;
      if (!((UnityEngine.Object) fsmTemplate != (UnityEngine.Object) null))
        return;
      EditorGUIUtility.PingObject((UnityEngine.Object) fsmTemplate);
    }

    private void DeleteTemplate(object userdata)
    {
      if (!Dialogs.AreYouSure(Strings.Dialog_Delete_Template, Strings.Label_You_cannot_undo_this_action))
        return;
      FsmTemplate fsmTemplate = userdata as FsmTemplate;
      if (!((UnityEngine.Object) fsmTemplate != (UnityEngine.Object) null))
        return;
      if ((UnityEngine.Object) FsmEditor.SelectedTemplate == (UnityEngine.Object) fsmTemplate)
        FsmEditor.Instance.OnSelectionChange();
      AssetDatabase.DeleteAsset(AssetDatabase.GetAssetPath((UnityEngine.Object) fsmTemplate));
    }

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message, LogColor logColor = LogColor.None)
    {
    }

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message, FsmTemplate template, LogColor logColor = LogColor.None)
    {
    }
  }
}

// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.CustomActionEditor
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System.Collections.Generic;
using System.Reflection;
using UnityEditor;

namespace HutongGames.PlayMakerEditor
{
  public abstract class CustomActionEditor
  {
    private readonly Dictionary<string, FieldInfo> fields = new Dictionary<string, FieldInfo>();
    public static float maxEditorWidth;
    public FsmStateAction target;

    public virtual bool showCategoryIcon => true;

    public virtual bool showEnabledCheckbox => true;

    public virtual void OnEnable()
    {
    }

    public virtual void OnFocus()
    {
    }

    public abstract bool OnGUI();

    public virtual void OnSceneGUI()
    {
    }

    public bool DrawDefaultInspector() => FsmEditor.ActionEditor.DrawDefaultInspector(this.target);

    public void EditField(string fieldName, string label = null, object[] attributes = null)
    {
      FieldInfo field;
      if (!this.fields.TryGetValue(fieldName, out field))
      {
        field = FsmEditor.ActionEditor.FindField(fieldName);
        if (field == null)
        {
          EditorGUILayout.HelpBox(Strings.Label_Could_Not_Find_Field + fieldName, MessageType.Error);
          return;
        }
        this.fields.Add(fieldName, field);
      }
      FsmEditor.ActionEditor.EditField(this.target, field, label, attributes);
    }

    public object EditField(System.Type type, object value) => FsmEditor.ActionEditor.EditField(type, value);

    public void Hint(string text) => EditorGUILayout.HelpBox(text, MessageType.None);

    public virtual void OnDisable()
    {
    }
  }
}

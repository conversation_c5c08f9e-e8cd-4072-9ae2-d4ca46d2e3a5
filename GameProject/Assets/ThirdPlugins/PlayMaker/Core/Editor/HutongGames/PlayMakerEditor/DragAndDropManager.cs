// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.DragAndDropManager
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System.ComponentModel;
using System.Diagnostics;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public static class DragAndDropManager
  {
    public static DragAndDropManager.DragMode mode;
    private static int dragCount;

    public static System.Type AddAction { get; private set; }

    public static FsmTemplate AddTemplate { get; private set; }

    public static StateInspector.DraggingActions MoveActions { get; private set; }

    public static UnityEngine.Object DraggedObject => !DragAndDropManager.IsDraggingObject ? (UnityEngine.Object) null : DragAndDrop.objectReferences[0];

    public static bool DragStarted => DragAndDropManager.dragCount > 0;

    public static bool Active => DragAndDropManager.IsDragging || DragAndDropManager.IsDraggingObject;

    public static bool IsDragging => (uint) DragAndDropManager.mode > 0U;

    public static bool IsDraggingSplitter => DragAndDropManager.mode == DragAndDropManager.DragMode.Splitter;

    public static bool IsDraggingObject => (uint) DragAndDrop.objectReferences.Length > 0U;

    private static void SetMode(DragAndDropManager.DragMode newMode) => DragAndDropManager.mode = newMode;

    public static bool StartMoveActions(StateInspector.DraggingActions actions)
    {
      int num = DragAndDropManager.StartDrag(DragAndDropManager.DragMode.MoveActions, (object) actions) ? 1 : 0;
      DragAndDropManager.MoveActions = actions;
      return num != 0;
    }

    public static void StartAddAction(System.Type actionType)
    {
      DragAndDropManager.StartDrag(DragAndDropManager.DragMode.AddAction, (object) actionType);
      DragAndDropManager.AddAction = actionType;
    }

    public static void StartAddTemplate(FsmTemplate template)
    {
      DragAndDropManager.StartDrag(DragAndDropManager.DragMode.AddTemplate, (object) template);
      DragAndDropManager.AddTemplate = template;
    }

    public static void DraggingSplitter(bool state) => DragAndDropManager.SetMode(state ? DragAndDropManager.DragMode.Splitter : DragAndDropManager.DragMode.None);

    private static bool StartDrag(DragAndDropManager.DragMode dragMode, object dragObject)
    {
      if (dragMode == DragAndDropManager.mode || DragAndDropManager.dragCount++ < 3)
        return false;
      DragAndDropManager.SetMode(dragMode);
      DragAndDrop.PrepareStartDrag();
      if (dragObject != null)
      {
        UnityEngine.Object @object = dragObject as UnityEngine.Object;
        if (@object != (UnityEngine.Object) null)
        {
          DragAndDrop.objectReferences = new UnityEngine.Object[1]
          {
            @object
          };
        }
        else
        {
          DragAndDrop.objectReferences = new UnityEngine.Object[0];
          DragAndDrop.SetGenericData(dragMode.ToString(), dragObject);
        }
      }
      DragAndDrop.StartDrag(ObjectNames.NicifyVariableName(dragMode.ToString()));
      Event.current.Use();
      return true;
    }

    public static void Update()
    {
      if (Event.current == null || !DragAndDropManager.IsDragging)
        return;
      if (Event.current.rawType == UnityEngine.EventType.MouseUp || Event.current.isKey && Event.current.keyCode == KeyCode.Escape)
        DragAndDropManager.Reset();
      DragAndDropManager.CheckForCancelled();
    }

    public static void SetStandardVisualMode() => DragAndDrop.visualMode = Event.current.shift ? DragAndDropVisualMode.Copy : DragAndDropVisualMode.Move;

    public static void Reset()
    {
      DragAndDropManager.SetMode(DragAndDropManager.DragMode.None);
      DragAndDropManager.AddAction = (System.Type) null;
      DragAndDropManager.MoveActions = (StateInspector.DraggingActions) null;
      DragAndDropManager.AddTemplate = (FsmTemplate) null;
      DragAndDropManager.dragCount = 0;
    }

    public static bool CheckForCancelled()
    {
      if (Event.current == null || !DragAndDropManager.IsDragging || (!Event.current.isMouse || Event.current.type != UnityEngine.EventType.MouseMove) || DragAndDrop.visualMode != DragAndDropVisualMode.None)
        return false;
      UnityEngine.Debug.Log((object) "DragAndDrop was canceled!");
      DragAndDropManager.Reset();
      FsmEditor.Repaint(true);
      return true;
    }

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message, LogColor logColor = LogColor.None)
    {
    }

    public enum DragMode
    {
      None,
      AddAction,
      MoveActions,
      AddTemplate,
      MoveProperty,
      Splitter,
    }
  }
}

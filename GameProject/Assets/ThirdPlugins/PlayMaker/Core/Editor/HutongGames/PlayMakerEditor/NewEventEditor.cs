// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.NewEventEditor
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using System;
using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class NewEventEditor
  {
    private bool debug = true;
    public Action<string> OnCommited;
    private string newEventName;
    private string validateError;
    private bool focus;

    public NewEventEditor(Fsm fsm) => this.Reset();

    public void Reset()
    {
      this.newEventName = "";
      this.validateError = "";
    }

    public void OnGUI()
    {
      GUILayout.BeginHorizontal();
      EditorGUI.BeginChangeCheck();
      GUI.SetNextControlName("focus");
      this.newEventName = EditorGUILayout.DelayedTextField(FsmEditorContent.AddEventLabel, this.newEventName);
      if (EditorGUI.EndChangeCheck())
      {
        this.validateError = FsmEditor.SelectedFsm.ValidateAddEvent(this.newEventName);
        if (string.IsNullOrEmpty(this.validateError))
        {
          this.AddEvent(this.newEventName);
          this.newEventName = "";
        }
      }
      if (FsmEditorGUILayout.BrowseButton(true, "Select Existing Event"))
        Menus.DoEventSelectionContextMenu(new GenericMenu.MenuFunction2(this.AddExistingEvent));
      GUILayout.EndHorizontal();
      if (!string.IsNullOrEmpty(this.validateError))
        GUILayout.Box(this.validateError, FsmEditorStyles.ErrorBox);
      if (!this.focus || Event.current.type != UnityEngine.EventType.Repaint)
        return;
      if (this.debug)
        Debug.Log((object) "Focus NewEventEditor");
      EditorHacks.FocusTextInControl("focus");
      this.focus = false;
    }

    public void Focus() => this.focus = true;

    private void AddExistingEvent(object userData)
    {
      if (!(userData is FsmEvent fsmEvent))
        return;
      this.AddEvent(fsmEvent.Name);
    }

    private void AddEvent(string eventName)
    {
      if (this.debug)
        Debug.Log((object) ("AddEvent: " + eventName));
      if (eventName.Replace(" ", "") == "")
      {
        EditorApplication.Beep();
        EditorUtility.DisplayDialog(Strings.Label_Add_Event, Strings.Error_Invalid_Name, Strings.OK);
      }
      else
      {
        if (!FsmEditor.DisconnectCheck())
          return;
        FsmEditor.RecordUndo(Strings.Command_Add_Event);
        EditorCommands.AddEvent(eventName);
        if (this.OnCommited == null)
          return;
        this.OnCommited(eventName);
      }
    }
  }
}

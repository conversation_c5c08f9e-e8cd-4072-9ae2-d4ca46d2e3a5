// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.FsmSelector
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.Editor;
using HutongGames.PlayMaker;
using JetBrains.Annotations;
using System;
using System.Collections.Generic;
using UnityEditor;

using UnityEngine;
using UnityEngine.SceneManagement;

namespace HutongGames.PlayMakerEditor
{
  public class FsmSelector : BaseEditorWindow
  {
    private static FsmSelector instance;
    private const float rowHeight = 18f;
    private const string tab = "     ";
    private const float CheckBoxWidth = 17f;
    private FsmSelector.FsmFilter fsmFilter;
    private float fsmColumnWidth;
    private bool showFullPath;
    private Fsm lastSelectedFsm;
    private Vector2 scrollPosition;
    private float scrollViewHeight;
    private static Rect selectedRect;
    private static bool autoScroll;
    private bool scrollbarsVisible;
    private readonly List<FsmSelector.FsmListItem> fsmList = new List<FsmSelector.FsmListItem>();

    public static void RefreshView()
    {
      if ((UnityEngine.Object) FsmSelector.instance == (UnityEngine.Object) null)
        return;
      FsmSelector.instance.BuildFsmList();
    }

    public override void InitWindowTitle() => this.SetTitle("FSMs");

    public override void Initialize()
    {
      this.isToolWindow = true;
      if ((UnityEngine.Object) FsmSelector.instance == (UnityEngine.Object) null)
        FsmSelector.instance = this;
      this.minSize = new Vector2(200f, 100f);
      this.BuildFsmList();
    }

    [UsedImplicitly]
    private void OnFocus()
    {
      if (!this.Initialized)
        return;
      this.BuildFsmList();
    }

    [UsedImplicitly]
    private void OnDisable()
    {
      if ((UnityEngine.Object) FsmSelector.instance == (UnityEngine.Object) this)
        FsmSelector.instance = (FsmSelector) null;
      this.fsmList.Clear();
    }

    public override void DoGUI()
    {
      if (Event.current.type == UnityEngine.EventType.MouseDown && Event.current.button == 0 && GUIUtility.hotControl == 0)
        GUIUtility.keyboardControl = 0;
      float num1 = this.position.width - 18f;
      if (FsmEditorSettings.FsmBrowserShowEnabledCheckboxes)
        num1 -= 19f;
      this.fsmColumnWidth = (float) ((double) num1 * 0.600000023841858 + 1.0);
      this.HandleKeyboardInput();
      this.DoToolbar();
      if (FsmEditorSettings.ShowHints)
        GUILayout.Box(Strings.Hint_FsmSelector, FsmEditorStyles.HintBox);
      this.DoTableHeader();
      GUILayout.BeginVertical();
      this.scrollPosition = GUILayout.BeginScrollView(this.scrollPosition);
      if (this.fsmList.Count == 0)
      {
        GUILayout.BeginHorizontal(FsmEditorStyles.TableRowBox);
        FsmEditorGUILayout.DisabledLabel("     " + Strings.Label_None);
        GUILayout.EndHorizontal();
      }
      int num2 = (int) ((double) this.scrollViewHeight / 18.0);
      int num3 = Mathf.Clamp((int) ((double) this.scrollPosition.y / 18.0) - 2, 0, Mathf.Max(0, this.fsmList.Count - num2));
      int num4 = Mathf.Min(this.fsmList.Count, num3 + num2 + 1);
      GUILayout.Space((float) num3 * 18f);
      for (int index = num3; index < num4; ++index)
        this.DoTableRow(this.fsmList[index]);
      GUILayout.Space(Mathf.Max(0.0f, (float) (this.fsmList.Count - num3 - num2) * 18f));
      GUILayout.EndScrollView();
      if (Event.current.type == UnityEngine.EventType.Repaint)
        this.scrollViewHeight = GUILayoutUtility.GetLastRect().height;
      this.scrollbarsVisible = (double) this.fsmList.Count * 18.0 > (double) this.scrollViewHeight;
      this.DoAutoScroll();
      GUILayout.FlexibleSpace();
      HighlighterHelper.EndVertical("FSM List");
      this.DoBottomPanel();
      if (Event.current.type != UnityEngine.EventType.MouseDown)
        return;
      FsmEditor.SelectNone();
    }

    private void DoBottomPanel()
    {
      GUILayout.BeginVertical(FsmEditorStyles.BottomBarBG);
      if (FsmEditor.SelectedFsm != null)
      {
        FsmEditor.SelectedFsm.Description = EditorGUILayout.TextArea(FsmEditor.SelectedFsm.Description, FsmEditorStyles.TextArea, GUILayout.MinHeight(44f));
        FsmEditorGUILayout.DrawHintText(FsmEditor.SelectedFsm.Description, Strings.Label_Description);
        HighlighterHelper.FromGUILayout("Description");
        EditorGUILayout.BeginHorizontal();
        FsmEditor.SelectedFsm.DocUrl = EditorGUILayout.TextField(FsmEditor.SelectedFsm.DocUrl);
        FsmEditorGUILayout.DrawHintText(FsmEditor.SelectedFsm.DocUrl, Strings.Label_Help_Url);
        if (string.IsNullOrEmpty(FsmEditor.SelectedFsm.DocUrl))
          GUI.enabled = false;
        if (FsmEditorGUILayout.HelpButton())
          Application.OpenURL(FsmEditor.SelectedFsm.DocUrl);
        HighlighterHelper.EndHorizontal("Help Url");
      }
      else
      {
        GUI.enabled = false;
        GUILayout.TextArea("", GUILayout.MinHeight(44f));
        HighlighterHelper.FromGUILayout("Description");
        EditorGUILayout.BeginHorizontal();
        GUILayout.TextField("");
        FsmEditorGUILayout.HelpButton();
        HighlighterHelper.EndHorizontal("Help Url");
      }
      GUI.enabled = true;
      GUILayout.EndVertical();
    }

    [UsedImplicitly]
    private void Update()
    {
      if (FsmEditorSettings.DisableEditorWhenPlaying || this.lastSelectedFsm == FsmEditor.SelectedFsm)
        return;
      this.lastSelectedFsm = FsmEditor.SelectedFsm;
      FsmSelector.autoScroll = true;
      this.Repaint();
    }

    private void DoAutoScroll()
    {
      if (FsmEditor.SelectedFsm == null || Event.current.type != UnityEngine.EventType.Repaint || !FsmSelector.autoScroll)
        return;
      if ((double) FsmSelector.selectedRect.y < 0.0)
      {
        this.scrollPosition.y += FsmSelector.selectedRect.y;
        this.Repaint();
      }
      else if ((double) FsmSelector.selectedRect.y + (double) FsmSelector.selectedRect.height > (double) this.scrollViewHeight)
      {
        this.scrollPosition.y += FsmSelector.selectedRect.y + FsmSelector.selectedRect.height - this.scrollViewHeight;
        this.Repaint();
      }
      FsmSelector.autoScroll = false;
    }

    private void DoToolbar()
    {
      GUILayout.BeginHorizontal(FsmEditorStyles.Toolbar);
      GUILayout.Space(4f);
      FsmSelector.FsmFilter mode = (FsmSelector.FsmFilter) EditorGUILayout.EnumPopup((Enum) this.fsmFilter, EditorStyles.toolbarPopup, GUILayout.Width(150f));
      if (this.fsmFilter != mode)
        this.SetFilterMode(mode);
      HighlighterHelper.FromGUILayout("FSM Filter");
      GUILayout.FlexibleSpace();
      if (FsmEditorGUILayout.ToolbarSettingsButton())
        this.GenerateSettingsMenu().ShowAsContext();
      GUILayout.EndHorizontal();
    }

    private void SetFilterMode(FsmSelector.FsmFilter mode)
    {
      this.fsmFilter = mode;
      this.BuildFsmList();
    }

    private void DoTableHeader()
    {
      GUILayout.BeginHorizontal(FsmEditorStyles.TopBarBG, GUILayout.Height(18f));
      if (FsmEditorSettings.FsmBrowserShowEnabledCheckboxes)
        GUILayout.Box(new GUIContent("", Strings.Label_Enabled), FsmEditorStyles.TableRowHeader, GUILayout.Width(19f));
      GUILayout.Space(18f);
      GUILayout.Box(new GUIContent(Strings.FSM, ""), FsmEditorStyles.TableRowHeader, GUILayout.Width(this.fsmColumnWidth - (this.scrollbarsVisible ? 10f : 0.0f)));
      GUILayout.Box(new GUIContent(Strings.Label_State, Strings.FsmSelector_Tootlip_State), FsmEditorStyles.TableRowHeader);
      GUILayout.EndHorizontal();
    }

    private void DoTableRow(FsmSelector.FsmListItem item)
    {
      if (item.isSceneHeader)
      {
        EditorGUIUtility.SetIconSize(FsmEditorStyles.IconSize);
        GUILayout.BeginHorizontal(FsmEditorStyles.TopBarBG, GUILayout.Height(18f));
        GUILayout.Label(item.nameLabel, FsmEditorStyles.TableSectionHeader);
        GUILayout.EndHorizontal();
        EditorGUIUtility.SetIconSize(Vector2.zero);
      }
      else
      {
        Fsm fsm = item.fsm;
        if (fsm == null || (UnityEngine.Object) fsm.Owner == (UnityEngine.Object) null)
          return;
        GUILayout.BeginHorizontal(GUILayout.Height(18f));
        if (FsmEditorSettings.FsmBrowserShowEnabledCheckboxes)
        {
          GUILayout.Space(2f);
          fsm.Owner.enabled = (GUILayout.Toggle((fsm.Owner.enabled ? 1 : 0) != 0, new GUIContent("", Strings.Label_Enabled), FsmEditorStyles.TableRowCheckBox, GUILayout.Width(17f)) ? 1 : 0) != 0;
        }
        GUIContent guiContent = !FsmEditorSettings.FsmBrowserShowFullPath || !this.showFullPath ? item.nameLabel : item.fullPathLabel;
        if (GameStateTracker.CurrentState != GameState.Stopped)
          item.UpdateStateLabel();
        EditorGUI.BeginDisabledGroup(!fsm.Owner.enabled);
        int num = FsmEditorGUILayout.TableRow(new GUIContent[2]
        {
          guiContent,
          item.stateLabel
        }, new float[2]{ 0.6f, 0.4f }, FsmEditor.SelectedFsm == fsm, fsm.HasErrors);
        EditorGUI.EndDisabledGroup();
        switch (num)
        {
          case 0:
            FsmEditor.SelectFsm(fsm);
            GUIUtility.ExitGUI();
            break;
          case 1:
            FsmEditor.SelectFsm(fsm);
            FsmEditor.SelectStateByName(item.stateLabel.text);
            GUIUtility.ExitGUI();
            break;
        }
        GUILayout.EndHorizontal();
        if (fsm != FsmEditor.SelectedFsm || Event.current.type != UnityEngine.EventType.Repaint)
          return;
        FsmSelector.selectedRect = GUILayoutUtility.GetLastRect();
        FsmSelector.selectedRect.y -= this.scrollPosition.y;
      }
    }

    private void SelectPrevious()
    {
      if (FsmEditor.SelectedFsm == null)
        return;
      int index = this.fsmList.FindIndex((Predicate<FsmSelector.FsmListItem>) (x => x.fsm == FsmEditor.SelectedFsm));
      if (index > 0)
      {
        Fsm fsm = this.fsmList[index - 1].fsm;
        if (fsm == null && index > 1)
        {
          fsm = this.fsmList[index - 2].fsm;
          if (fsm == null)
            return;
        }
        FsmEditor.SelectFsm(fsm);
      }
      FsmSelector.autoScroll = true;
    }

    private void SelectNext()
    {
      if (FsmEditor.SelectedFsm == null)
        return;
      int index = this.fsmList.FindIndex((Predicate<FsmSelector.FsmListItem>) (x => x.fsm == FsmEditor.SelectedFsm));
      if (index < this.fsmList.Count - 1)
        FsmEditor.SelectFsm(this.fsmList[index + 1].fsm ?? this.fsmList[index + 2].fsm);
      FsmSelector.autoScroll = true;
    }

    private void HandleKeyboardInput()
    {
      if (!Keyboard.IsGuiEventKeyboardShortcut())
        return;
      switch (Event.current.keyCode)
      {
        case KeyCode.Escape:
          FsmEditor.SelectNone();
          break;
        case KeyCode.UpArrow:
          this.SelectPrevious();
          break;
        case KeyCode.DownArrow:
          this.SelectNext();
          break;
        default:
          return;
      }
      Event.current.Use();
      GUIUtility.ExitGUI();
    }

    private GenericMenu GenerateSettingsMenu()
    {
      GenericMenu genericMenu = new GenericMenu();
      genericMenu.AddItem(new GUIContent(Strings.Menu_Show_Full_FSM_Path), FsmEditorSettings.FsmBrowserShowFullPath, new GenericMenu.MenuFunction(this.ToggleShowFullPath));
      genericMenu.AddItem(new GUIContent("Show Enabled Checkboxes"), FsmEditorSettings.FsmBrowserShowEnabledCheckboxes, new GenericMenu.MenuFunction(this.ToggleShowEnabledCheckboxes));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Show_Disabled_FSMs), FsmEditorSettings.FsmBrowserShowDisabled, new GenericMenu.MenuFunction(this.ToggleShowDisabled));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Show_FSMs_in_Prefabs), FsmEditorSettings.FsmBrowserShowPrefabs, new GenericMenu.MenuFunction(this.ToggleShowPrefabs));
      genericMenu.AddItem(new GUIContent(Strings.Menu_Hide_Prefabs_When_Playing), FsmEditorSettings.FsmBrowserHidePrefabsWhenPlaying, new GenericMenu.MenuFunction(this.ToggleHidePrefabsWhenPlaying));
      return genericMenu;
    }

    private void ToggleShowEnabledCheckboxes()
    {
      FsmEditorSettings.FsmBrowserShowEnabledCheckboxes = !FsmEditorSettings.FsmBrowserShowEnabledCheckboxes;
      FsmEditorSettings.SaveSettings();
      this.Repaint();
    }

    private void ToggleShowFullPath()
    {
      FsmEditorSettings.FsmBrowserShowFullPath = !FsmEditorSettings.FsmBrowserShowFullPath;
      FsmEditorSettings.SaveSettings();
      this.Repaint();
    }

    private void ToggleShowDisabled()
    {
      FsmEditorSettings.FsmBrowserShowDisabled = !FsmEditorSettings.FsmBrowserShowDisabled;
      FsmEditorSettings.SaveSettings();
      this.BuildFsmList();
      this.Repaint();
    }

    private void ToggleShowPrefabs()
    {
      FsmEditorSettings.FsmBrowserShowPrefabs = !FsmEditorSettings.FsmBrowserShowPrefabs;
      FsmEditorSettings.SaveSettings();
      this.BuildFsmList();
      this.Repaint();
    }

    private void ToggleHidePrefabsWhenPlaying()
    {
      FsmEditorSettings.FsmBrowserHidePrefabsWhenPlaying = !FsmEditorSettings.FsmBrowserHidePrefabsWhenPlaying;
      FsmEditorSettings.SaveSettings();
      this.BuildFsmList();
      this.Repaint();
    }

    private void ToggleShowAllStates()
    {
      FsmEditorSettings.FsmBrowserShowAllStates = !FsmEditorSettings.FsmBrowserShowAllStates;
      FsmEditorSettings.SaveSettings();
      this.Repaint();
    }

    private void BuildFsmList()
    {
      this.fsmList.Clear();
      this.showFullPath = true;
      if (this.fsmFilter == FsmSelector.FsmFilter.RecentlySelected)
      {
        foreach (Fsm recentlySelectedFsM in FsmEditor.SelectionHistory.GetRecentlySelectedFSMs())
          this.AddFsmListItem(recentlySelectedFsM);
      }
      else
      {
        foreach (Fsm fsm in FsmEditor.FsmList)
        {
          if (fsm != null && !((UnityEngine.Object) fsm.UsedInTemplate != (UnityEngine.Object) null))
          {
            switch (this.fsmFilter)
            {
              case FsmSelector.FsmFilter.All:
                this.AddFsmListItem(fsm);
                continue;
              case FsmSelector.FsmFilter.OnSelectedObject:
                if (Selection.Contains((UnityEngine.Object) fsm.GameObject))
                  this.AddFsmListItem(fsm);
                this.showFullPath = false;
                continue;
              case FsmSelector.FsmFilter.WithErrors:
                if (fsm.HasErrors)
                {
                  this.AddFsmListItem(fsm);
                  continue;
                }
                continue;
              default:
                continue;
            }
          }
        }
      }
      this.fsmList.Sort(new Comparison<FsmSelector.FsmListItem>(FsmSelector.FsmListItem.CompareByHierarchyOrder));
      if (SceneManager.sceneCount > 1)
        this.InsertSceneHeaders();
      this.Repaint();
    }

    private void InsertSceneHeaders()
    {
      for (int i = 0; i < SceneManager.sceneCount; i++)
      {
        int index = this.fsmList.FindIndex((Predicate<FsmSelector.FsmListItem>) (x => x.sceneIndex == i));
        if (index != -1)
          this.fsmList.Insert(index, new FsmSelector.FsmListItem(i));
      }
    }

    private void AddFsmListItem(Fsm fsm)
    {
      if (fsm == null || (UnityEngine.Object) fsm.Owner == (UnityEngine.Object) null || !FsmEditorSettings.FsmBrowserShowDisabled && !fsm.Owner.enabled || (!FsmEditorSettings.FsmBrowserShowPrefabs || EditorApplication.isPlaying && FsmEditorSettings.FsmBrowserHidePrefabsWhenPlaying) && FsmPrefabs.IsPrefab(fsm))
        return;
      UnityEditor.SceneManagement.PrefabStage currentPrefabStage = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage();
      if (currentPrefabStage != null && !currentPrefabStage.IsPartOfPrefabContents(fsm.GameObject))
        return;
      this.fsmList.Add(new FsmSelector.FsmListItem(fsm));
    }

    public enum FsmFilter
    {
      All,
      OnSelectedObject,
      RecentlySelected,
      WithErrors,
    }

    private class FsmListItem
    {
      public readonly Fsm fsm;
      public readonly GUIContent nameLabel = new GUIContent();
      public readonly GUIContent fullPathLabel = new GUIContent();
      public readonly GUIContent stateLabel = new GUIContent();
      public readonly int sceneIndex;
      public readonly bool isSceneHeader;
      private int hierarchyOrder;

      public FsmListItem(int sceneIndex)
      {
        this.isSceneHeader = true;
        this.sceneIndex = sceneIndex;
        this.nameLabel.text = SceneManager.GetSceneAt(sceneIndex).name;
        this.nameLabel.image = EditorGUIUtility.ObjectContent((UnityEngine.Object) null, typeof (SceneAsset)).image;
      }

      public FsmListItem(Fsm fsm)
      {
        this.fsm = fsm;
        string fullFsmLabel = Labels.GetFullFsmLabel(fsm);
        this.fullPathLabel.text = fullFsmLabel;
        this.fullPathLabel.tooltip = fullFsmLabel;
        this.nameLabel.text = Labels.GetFsmLabel(fsm);
        this.nameLabel.tooltip = fullFsmLabel;
        Scene scene = fsm.GameObject.scene;
        for (int index = 0; index < SceneManager.sceneCount; ++index)
        {
          if (SceneManager.GetSceneAt(index) == scene)
            this.sceneIndex = index;
        }
        GameObject gameObject = fsm.GameObject;
        this.hierarchyOrder = 0;
        if (scene.IsValid())
        {
          GameObject[] rootGameObjects = scene.GetRootGameObjects();
          int index = 0;
          while (index < rootGameObjects.Length && !this.DepthSearch(rootGameObjects[index], gameObject))
            ++index;
        }
        this.UpdateStateLabel();
      }

      public void UpdateStateLabel()
      {
        this.stateLabel.text = GameStateTracker.CurrentState != GameState.Stopped ? (this.fsm.ActiveState == null ? Strings.FsmSelector_no_active_state : this.fsm.ActiveState.Name) : ((UnityEngine.Object) this.fsm.Template != (UnityEngine.Object) null ? this.fsm.Template.fsm.StartState : this.fsm.StartState);
        this.stateLabel.tooltip = this.stateLabel.text;
      }

      public static int CompareByHierarchyOrder(
        FsmSelector.FsmListItem a,
        FsmSelector.FsmListItem b)
      {
        int num = a.sceneIndex.CompareTo(b.sceneIndex);
        return num == 0 ? a.hierarchyOrder.CompareTo(b.hierarchyOrder) : num;
      }

      public static int CompareNamesAlphabetical(
        FsmSelector.FsmListItem a,
        FsmSelector.FsmListItem b)
      {
        int num = a.sceneIndex.CompareTo(b.sceneIndex);
        return num == 0 ? string.Compare(a.nameLabel.text, b.nameLabel.text, StringComparison.Ordinal) : num;
      }

      public static int CompareFullPathAlphabetical(
        FsmSelector.FsmListItem a,
        FsmSelector.FsmListItem b)
      {
        int num = a.sceneIndex.CompareTo(b.sceneIndex);
        return num == 0 ? string.Compare(a.fullPathLabel.text, b.fullPathLabel.text, StringComparison.Ordinal) : num;
      }

      private bool DepthSearch(GameObject parentGameObject, GameObject find)
      {
        ++this.hierarchyOrder;
        if ((UnityEngine.Object) parentGameObject == (UnityEngine.Object) find)
          return true;
        foreach (Component component in parentGameObject.transform)
        {
          if (this.DepthSearch(component.gameObject, find))
            return true;
        }
        return false;
      }
    }
  }
}

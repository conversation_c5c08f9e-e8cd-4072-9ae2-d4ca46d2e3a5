// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.Graph.NodeType
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;

namespace HutongGames.PlayMakerEditor.Graph
{
  [Serializable]
  public enum NodeType
  {
    None = 0,
    Fsm = 1,
    State = 2,
    StateTransition = 3,
    GlobalTransition = 4,
    Group = 20, // 0x00000014
    Panel = 21, // 0x00000015
    Text = 22, // 0x00000016
    Title = 23, // 0x00000017
    Watermark = 24, // 0x00000018
    BezierLink = 100, // 0x00000064
    CircuitLink = 101, // 0x00000065
    DirectLink = 102, // 0x00000066
    LoadErrorNode = 10000, // 0x00002710
  }
}

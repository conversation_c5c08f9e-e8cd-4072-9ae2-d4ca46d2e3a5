// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.Link
// Assembly: PlayMakerEditor, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public class Link
  {
    public FsmState FromState { get; set; }

    public int TransitionIndex { get; set; }

    public static void DrawArrowHead(
      Texture leftArrow,
      Vector2 pos,
      Color color,
      bool flipTexture,
      float scale)
    {
      Color color1 = GUI.color;
      GUI.color = color;
      if (!flipTexture)
      {
        GUI.DrawTexture(new Rect(pos.x, pos.y - (float) leftArrow.height * 0.5f, (float) leftArrow.width * scale, (float) leftArrow.height), leftArrow);
      }
      else
      {
        Matrix4x4 matrix = GUI.matrix;
        GUIUtility.ScaleAroundPivot(new Vector2(-1f, 1f), pos);
        GUI.DrawTexture(new Rect(pos.x, pos.y - (float) leftArrow.height * 0.5f, (float) leftArrow.width * scale, (float) leftArrow.height), leftArrow);
        GUI.matrix = matrix;
      }
      GUI.color = color1;
    }
  }
}

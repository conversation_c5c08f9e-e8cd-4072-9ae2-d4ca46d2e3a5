// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.PropertyDrawer
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using UnityEditor;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public abstract class PropertyDrawer
  {
    public abstract object OnGUI(
      GUIContent label,
      object obj,
      bool isSceneObject,
      params object[] attributes);

    public void EditField(string fieldName, object fieldValue, object[] attributes) => FsmEditor.ActionEditor.EditField(fieldName, ObjectNames.NicifyVariableName(fieldName), fieldValue, attributes);

    public void EditField(string fieldName, string label, object fieldValue, object[] attributes) => FsmEditor.ActionEditor.EditField(fieldName, label, fieldValue, attributes);

    public object EditField(string label, System.Type type, object obj, object[] attributes) => FsmEditor.ActionEditor.EditField(label, type, obj, attributes);
  }
}

// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.DebugFlow
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using HutongGames.PlayMaker;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  [Localizable(false)]
  public static class DebugFlow
  {
    public static DebugFlow.LogEntrySelectedHandler LogEntrySelected;
    private static FsmLogEntry lastEnter;
    private static int lastEnterIndex;
    private static Dictionary<Fsm, FsmVariables> variablesCache = new Dictionary<Fsm, FsmVariables>();
    private static FsmVariables globalVariablesCache;

    public static bool Active { get; private set; }

    public static FsmLog SelectedLog { get; private set; }

    public static FsmLogEntry SelectedLogEntry { get; private set; }

    public static int SelectedLogEntryIndex { get; private set; }

    public static float CurrentDebugTime { get; private set; }

    public static FsmState DebugState { get; private set; }

    private static int StartedDebugFrame { get; set; }

    private static int CurrentDebugFrame { get; set; }

    public static bool ActiveAndScrubbing => DebugFlow.Active && DebugFlow.SelectedLogEntryIndex < DebugFlow.lastEnterIndex;

    public static void Start(Fsm fsm)
    {
      DebugFlow.UpdateTime();
      DebugFlow.Active = true;
      DebugFlow.SyncFsmLog(fsm);
      DebugFlow.StartedDebugFrame = Time.frameCount;
      DebugFlow.variablesCache = new Dictionary<Fsm, FsmVariables>();
      foreach (Fsm fsm1 in FsmEditor.FsmList)
      {
        if (fsm1.EnableDebugFlow)
          DebugFlow.variablesCache.Add(fsm1, new FsmVariables(fsm1.Variables));
      }
      DebugFlow.globalVariablesCache = new FsmVariables(FsmVariables.GlobalVariables);
    }

    public static void SyncFsmLog(Fsm fsm)
    {
      if (fsm == null)
      {
        DebugFlow.SelectedLog = (FsmLog) null;
      }
      else
      {
        DebugFlow.SelectedLog = fsm.MyLog;
        if (!DebugFlow.Active || DebugFlow.SelectedLog == null || DebugFlow.SelectedLog.Entries == null)
          return;
        DebugFlow.SelectMostRecentLogEntry(DebugFlow.CurrentDebugFrame);
        DebugFlow.lastEnter = DebugFlow.SelectedLogEntry;
        DebugFlow.lastEnterIndex = DebugFlow.SelectedLog.Entries.IndexOf(DebugFlow.lastEnter);
      }
    }

    public static void Stop()
    {
      if (!DebugFlow.Active)
        return;
      DebugFlow.Active = false;
      if (!Application.isPlaying)
        return;
      DebugFlow.SelectedLogEntry = (FsmLogEntry) null;
      DebugFlow.Active = false;
      foreach (Fsm key in new List<Fsm>((IEnumerable<Fsm>) FsmEditor.FsmList))
      {
        if (key != null && key.EnableDebugFlow && DebugFlow.variablesCache != null)
        {
          FsmVariables source;
          DebugFlow.variablesCache.TryGetValue(key, out source);
          if (source != null)
            key.Variables.ApplyVariableValues(source);
          else
            UnityEngine.Debug.LogWarning((object) "DebugFlow: Missing Cached Variables...");
        }
      }
      FsmVariables.GlobalVariables.ApplyVariableValues(DebugFlow.globalVariablesCache);
      DebugFlow.variablesCache.Clear();
      DebugFlow.globalVariablesCache = (FsmVariables) null;
    }

    public static void Cleanup()
    {
      DebugFlow.variablesCache = (Dictionary<Fsm, FsmVariables>) null;
      DebugFlow.globalVariablesCache = (FsmVariables) null;
      DebugFlow.SelectedLog = (FsmLog) null;
      DebugFlow.SelectedLogEntry = (FsmLogEntry) null;
      DebugFlow.DebugState = (FsmState) null;
      DebugFlow.lastEnter = (FsmLogEntry) null;
    }

    public static void UpdateTime()
    {
      DebugFlow.CurrentDebugTime = FsmTime.RealtimeSinceStartup;
      DebugFlow.CurrentDebugFrame = Time.frameCount;
    }

    public static void Refresh() => DebugFlow.SetDebugTime(DebugFlow.CurrentDebugTime);

    public static void SetDebugTime(float time)
    {
      DebugFlow.CurrentDebugTime = time;
      foreach (Fsm fsm in FsmEditor.FsmList)
      {
        if (fsm != null)
        {
          FsmLogEntry closestLogEntry = DebugFlow.FindClosestLogEntry(fsm.MyLog, time);
          if (FsmEditor.SelectedFsm == fsm)
            DebugFlow.SelectLogEntry(closestLogEntry, false);
          else
            DebugFlow.RestoreNearestVariables(closestLogEntry);
        }
      }
      FsmLogger.SetDebugFlowTime(time);
    }

    public static void SelectLogEntry(FsmLogEntry logEntry, bool updateTime = true)
    {
      if (logEntry == null)
        return;
      DebugFlow.SelectedLog = logEntry.Log;
      DebugFlow.SelectedLogEntry = logEntry;
      DebugFlow.DebugState = logEntry.State;
      DebugFlow.SelectedLogEntryIndex = logEntry.GetIndex();
      if (updateTime)
      {
        DebugFlow.CurrentDebugTime = logEntry.Time;
        DebugFlow.CurrentDebugFrame = logEntry.FrameCount;
      }
      FsmEditor.SelectState(logEntry.State, true);
      if (logEntry.Action != null)
        FsmEditor.SelectAction(logEntry.Action);
      if (FsmEditorSettings.EnableDebugFlow && DebugFlow.SelectedLog.Fsm.EnableDebugFlow && DebugFlow.SelectedLogEntryIndex < DebugFlow.lastEnterIndex)
        DebugFlow.RestoreNearestVariables(logEntry);
      if (DebugFlow.LogEntrySelected != null)
        DebugFlow.LogEntrySelected(logEntry);
      FsmEditor.Repaint(true);
    }

    private static void RestoreNearestVariables(FsmLogEntry logEntry)
    {
      if (logEntry == null)
        return;
      if (logEntry.LogType == FsmLogType.EnterState || logEntry.LogType == FsmLogType.ExitState)
        DebugFlow.RestoreVariables(logEntry);
      else if (logEntry.Event == FsmEvent.Finished)
      {
        FsmLogEntry nextLogEntry = DebugFlow.FindNextLogEntry(logEntry, FsmLogType.ExitState);
        if (nextLogEntry == null)
          return;
        DebugFlow.RestoreVariables(nextLogEntry);
      }
      else if (DebugFlow.SelectedLogEntryIndex == 0)
      {
        FsmLogEntry nextLogEntry = DebugFlow.FindNextLogEntry(logEntry, FsmLogType.EnterState);
        if (nextLogEntry == null)
          return;
        DebugFlow.RestoreVariables(nextLogEntry);
      }
      else
      {
        FsmLogEntry prevLogEntry = DebugFlow.FindPrevLogEntry(logEntry);
        if (prevLogEntry == null)
          return;
        DebugFlow.RestoreVariables(prevLogEntry);
      }
    }

    private static void RestoreVariables(FsmLogEntry logEntry)
    {
      if (logEntry == null)
      {
        UnityEngine.Debug.Log((object) "Bad Log Entry!");
      }
      else
      {
        Fsm fsm = logEntry.Fsm;
        if (!fsm.EnableDebugFlow)
          return;
        if (fsm == null)
        {
          UnityEngine.Debug.Log((object) "Fsm == null!!");
        }
        else
        {
          if (logEntry.FsmVariablesCopy != null)
            fsm.Variables.ApplyVariableValues(logEntry.FsmVariablesCopy);
          else
            UnityEngine.Debug.LogError((object) "Missing Local Variables Cache!");
          if (logEntry.GlobalVariablesCopy != null)
            FsmVariables.GlobalVariables.ApplyVariableValues(logEntry.GlobalVariablesCopy);
          else
            UnityEngine.Debug.LogError((object) "Missing global Variables Cache!");
          if (FsmEditor.SelectedFsm != fsm)
            return;
          FsmEditor.VariablesManager.UpdateView();
          GlobalVariablesWindow.UpdateView();
          FsmEditor.Repaint();
        }
      }
    }

    private static void SelectMostRecentLogEntry(int fromFrame) => DebugFlow.SelectLogEntry(DebugFlow.FindMostRecentLogEntry(fromFrame), false);

    public static void SelectPrevTransition() => DebugFlow.SelectLogEntry(DebugFlow.FindPrevLogEntry(DebugFlow.SelectedLogEntry));

    public static void SelectNextTransition() => DebugFlow.SelectLogEntry(DebugFlow.FindNextLogEntry(DebugFlow.SelectedLogEntry, FsmLogType.EnterState, FsmLogType.Break));

    private static FsmLogEntry FindClosestLogEntry(FsmLog fsmLog, float time)
    {
      FsmLogEntry fsmLogEntry = (FsmLogEntry) null;
      foreach (FsmLogEntry entry in fsmLog.Entries)
      {
        if ((double) entry.Time <= (double) time)
        {
          if (entry.LogType == FsmLogType.EnterState)
            fsmLogEntry = entry;
        }
        else
          break;
      }
      return fsmLogEntry;
    }

    private static FsmLogEntry FindMostRecentLogEntry(int fromFrame) => DebugFlow.FindMostRecentLogEntry(DebugFlow.SelectedLog, fromFrame);

    private static FsmLogEntry FindMostRecentLogEntry(FsmLog fsmLog, int fromFrame)
    {
      if (fsmLog == null || fsmLog.Entries == null)
        return (FsmLogEntry) null;
      FsmLogEntry fsmLogEntry = (FsmLogEntry) null;
      foreach (FsmLogEntry entry in fsmLog.Entries)
      {
        if (entry.LogType == FsmLogType.EnterState || entry.LogType == FsmLogType.SendEvent || entry.LogType == FsmLogType.Break)
          fsmLogEntry = entry;
      }
      return fsmLogEntry;
    }

    private static FsmLogEntry FindPrevLogEntry(
      FsmLogEntry fromEntry,
      FsmLogType logType = FsmLogType.EnterState)
    {
      if (fromEntry == null)
        return (FsmLogEntry) null;
      FsmLog log = fromEntry.Log;
      if (log == null || log.Entries == null)
        return (FsmLogEntry) null;
      FsmLogEntry fsmLogEntry = (FsmLogEntry) null;
      foreach (FsmLogEntry entry in log.Entries)
      {
        if (entry != fromEntry)
        {
          if (entry.LogType == logType)
            fsmLogEntry = entry;
        }
        else
          break;
      }
      return fsmLogEntry;
    }

    private static FsmLogEntry FindNextLogEntry(
      FsmLogEntry fromEntry,
      params FsmLogType[] logTypes)
    {
      FsmLog log = fromEntry.Log;
      if (log == null)
        return (FsmLogEntry) null;
      for (int index = DebugFlow.SelectedLog.Entries.IndexOf(fromEntry) + 1; index < log.Entries.Count; ++index)
      {
        FsmLogEntry entry = log.Entries[index];
        foreach (FsmLogType logType in logTypes)
        {
          if (entry.LogType == logType)
            return entry;
        }
      }
      return (FsmLogEntry) null;
    }

    public static FsmLogEntry GetLastTransition() => DebugFlow.FindPrevLogEntry(DebugFlow.SelectedLogEntry, FsmLogType.Transition);

    [Conditional("DEBUG_LOG")]
    private static void DebugLog(object message) => UnityEngine.Debug.Log((object) ("DebugFlow: " + message));

    public delegate void LogEntrySelectedHandler(FsmLogEntry logEntry);
  }
}

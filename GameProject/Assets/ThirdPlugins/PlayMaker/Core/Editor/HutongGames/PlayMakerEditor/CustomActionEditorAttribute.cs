// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMakerEditor.CustomActionEditorAttribute
// Assembly: PlayMakerEditor, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: CC413F89-440A-4CAD-A8BA-B7C7A4014B12
// Assembly location: E:\UnityProjects\My project (4)\Assets\PlayMaker\Editor\PlayMakerEditor.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMakerEditor
{
  public sealed class CustomActionEditorAttribute : Attribute
  {
    public System.Type InspectedType;

    public CustomActionEditorAttribute(System.Type inspectedType)
    {
      if (inspectedType == null)
        Debug.LogError((object) Strings.Error_Failed_to_load_Custom_Action_Editor);
      this.InspectedType = inspectedType;
    }
  }
}

// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.ActionsInternal.Header
// Assembly: PlayMaker, Version=*******, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

namespace HutongGames.PlayMaker.ActionsInternal
{
  [ActionCategory(ActionCategory.PlayMakerInternal)]
  [Tooltip("Large header used to organize actions list. Double-click to edit.")]
  public class Header : FsmStateAction
  {
    [UIHint(UIHint.Comment)]
    public string comment;
    public int colorId;

    public override void Reset() => this.comment = "Double-click to edit comment.";

    public override void Awake() => this.Enabled = false;
  }
}

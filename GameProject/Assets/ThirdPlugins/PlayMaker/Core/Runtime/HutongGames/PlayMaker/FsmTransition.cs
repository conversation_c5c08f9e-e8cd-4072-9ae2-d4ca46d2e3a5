// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmTransition
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmTransition : IEquatable<FsmTransition>
  {
    [SerializeField]
    private FsmEvent fsmEvent;
    [SerializeField]
    private string toState;
    [SerializeField]
    private FsmTransition.CustomLinkStyle linkStyle;
    [SerializeField]
    private FsmTransition.CustomLinkConstraint linkConstraint;
    [SerializeField]
    private FsmTransition.CustomLinkTarget linkTarget;
    [SerializeField]
    private byte colorIndex;
    [NonSerialized]
    private FsmState toFsmState;

    public FsmTransition()
    {
    }

    public FsmTransition(FsmTransition source)
    {
      this.fsmEvent = source.FsmEvent;
      this.toState = source.toState;
      this.linkStyle = source.linkStyle;
      this.linkConstraint = source.linkConstraint;
      this.colorIndex = source.colorIndex;
    }

    public FsmEvent FsmEvent
    {
      get => this.fsmEvent;
      set => this.fsmEvent = value;
    }

    public string ToState
    {
      get => this.toState;
      set => this.toState = value;
    }

    public FsmState ToFsmState
    {
      get => this.toFsmState;
      set => this.toFsmState = value;
    }

    public FsmTransition.CustomLinkStyle LinkStyle
    {
      get => this.linkStyle;
      set => this.linkStyle = value;
    }

    public FsmTransition.CustomLinkConstraint LinkConstraint
    {
      get => this.linkConstraint;
      set => this.linkConstraint = value;
    }

    public FsmTransition.CustomLinkTarget LinkTarget
    {
      get => this.linkTarget;
      set => this.linkTarget = value;
    }

    public int ColorIndex
    {
      get => (int) this.colorIndex;
      set => this.colorIndex = (byte) Mathf.Clamp(value, 0, PlayMakerPrefs.Colors.Length - 1);
    }

    public string EventName => !FsmEvent.IsNullOrEmpty(this.fsmEvent) ? this.fsmEvent.Name : string.Empty;

    public bool Equals(FsmTransition other)
    {
      if (other == null)
        return false;
      if (this == other)
        return true;
      return !(other.toState != this.toState) && other.EventName == this.EventName;
    }

    public enum CustomLinkStyle : byte
    {
      Default,
      Bezier,
      Circuit,
      Direct,
    }

    public enum CustomLinkConstraint : byte
    {
      None,
      LockLeft,
      LockRight,
    }

    public enum CustomLinkTarget : byte
    {
      None,
      LockLeft,
      LockRight,
    }
  }
}

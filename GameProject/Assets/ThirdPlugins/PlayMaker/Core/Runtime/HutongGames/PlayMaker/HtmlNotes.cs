// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.HtmlNotes
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System;
using System.Collections.Generic;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class HtmlNotes
  {
    public string title = "Title";
    public string rawText = "Double-Click to Edit";
    public List<UnityEngine.Object> objects = new List<UnityEngine.Object>();
  }
}

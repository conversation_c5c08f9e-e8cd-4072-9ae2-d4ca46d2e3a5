// Decompiled with JetBrains decompiler
// Type: HutongGames.PlayMaker.FsmVariables
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using HutongGames.Utility;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace HutongGames.PlayMaker
{
  [Serializable]
  public class FsmVariables
  {
    private Dictionary<string, NamedVariable> _variableLookup;
    [NonSerialized]
    private NamedVariable[] _allVariables;
    [NonSerialized]
    private List<NamedVariable> _emptyVariables;
    [SerializeField]
    private FsmFloat[] floatVariables;
    [SerializeField]
    private FsmInt[] intVariables;
    [SerializeField]
    private FsmBool[] boolVariables;
    [SerializeField]
    private FsmString[] stringVariables;
    [SerializeField]
    private FsmVector2[] vector2Variables;
    [SerializeField]
    private FsmVector3[] vector3Variables;
    [SerializeField]
    private FsmColor[] colorVariables;
    [SerializeField]
    private FsmRect[] rectVariables;
    [SerializeField]
    private FsmQuaternion[] quaternionVariables;
    [SerializeField]
    private FsmGameObject[] gameObjectVariables;
    [SerializeField]
    private FsmObject[] objectVariables;
    [SerializeField]
    private FsmMaterial[] materialVariables;
    [SerializeField]
    private FsmTexture[] textureVariables;
    [SerializeField]
    private FsmArray[] arrayVariables;
    [SerializeField]
    private FsmEnum[] enumVariables;
    [SerializeField]
    private string[] categories = new string[1]{ "" };
    [SerializeField]
    private int[] variableCategoryIDs = new int[0];

    public static PlayMakerGlobals GlobalsComponent => PlayMakerGlobals.Instance;

    public static FsmVariables GlobalVariables => PlayMakerGlobals.Instance.Variables;

    public static bool GlobalVariablesSynced { get; set; }

    private Dictionary<string, NamedVariable> variableLookup
    {
      get
      {
        if (this._variableLookup == null)
          this.Init();
        return this._variableLookup;
      }
    }

    private NamedVariable[] allVariables
    {
      get
      {
        if (this._allVariables == null)
          this.Init();
        return this._allVariables;
      }
    }

    private List<NamedVariable> emptyVariables
    {
      get
      {
        if (this._emptyVariables == null)
          this.Init();
        return this._emptyVariables;
      }
    }

    public string[] Categories
    {
      get => this.categories;
      set => this.categories = value;
    }

    public int[] CategoryIDs
    {
      get => this.variableCategoryIDs;
      set => this.variableCategoryIDs = value;
    }

    public FsmVariables()
    {
    }

    public FsmVariables(FsmVariables source)
    {
      if (source == null)
        return;
      this.categories = new string[source.categories.Length];
      Array.Copy((Array) source.categories, (Array) this.categories, source.categories.Length);
      if (source.floatVariables != null)
      {
        this.floatVariables = new FsmFloat[source.floatVariables.Length];
        for (int index = 0; index < source.floatVariables.Length; ++index)
          this.floatVariables[index] = new FsmFloat(source.floatVariables[index]);
      }
      if (source.intVariables != null)
      {
        this.intVariables = new FsmInt[source.intVariables.Length];
        for (int index = 0; index < source.intVariables.Length; ++index)
          this.intVariables[index] = new FsmInt(source.intVariables[index]);
      }
      if (source.boolVariables != null)
      {
        this.boolVariables = new FsmBool[source.boolVariables.Length];
        for (int index = 0; index < source.boolVariables.Length; ++index)
          this.boolVariables[index] = new FsmBool(source.boolVariables[index]);
      }
      if (source.gameObjectVariables != null)
      {
        this.gameObjectVariables = new FsmGameObject[source.gameObjectVariables.Length];
        for (int index = 0; index < source.gameObjectVariables.Length; ++index)
          this.gameObjectVariables[index] = new FsmGameObject(source.gameObjectVariables[index]);
      }
      if (source.colorVariables != null)
      {
        this.colorVariables = new FsmColor[source.colorVariables.Length];
        for (int index = 0; index < source.colorVariables.Length; ++index)
          this.colorVariables[index] = new FsmColor(source.colorVariables[index]);
      }
      if (source.vector2Variables != null)
      {
        this.vector2Variables = new FsmVector2[source.vector2Variables.Length];
        for (int index = 0; index < source.vector2Variables.Length; ++index)
          this.vector2Variables[index] = new FsmVector2(source.vector2Variables[index]);
      }
      if (source.vector3Variables != null)
      {
        this.vector3Variables = new FsmVector3[source.vector3Variables.Length];
        for (int index = 0; index < source.vector3Variables.Length; ++index)
          this.vector3Variables[index] = new FsmVector3(source.vector3Variables[index]);
      }
      if (source.rectVariables != null)
      {
        this.rectVariables = new FsmRect[source.rectVariables.Length];
        for (int index = 0; index < source.rectVariables.Length; ++index)
          this.rectVariables[index] = new FsmRect(source.rectVariables[index]);
      }
      if (source.quaternionVariables != null)
      {
        this.quaternionVariables = new FsmQuaternion[source.quaternionVariables.Length];
        for (int index = 0; index < source.quaternionVariables.Length; ++index)
          this.quaternionVariables[index] = new FsmQuaternion(source.quaternionVariables[index]);
      }
      if (source.objectVariables != null)
      {
        this.objectVariables = new FsmObject[source.objectVariables.Length];
        for (int index = 0; index < source.objectVariables.Length; ++index)
          this.objectVariables[index] = new FsmObject(source.objectVariables[index]);
      }
      if (source.materialVariables != null)
      {
        this.materialVariables = new FsmMaterial[source.materialVariables.Length];
        for (int index = 0; index < source.materialVariables.Length; ++index)
          this.materialVariables[index] = new FsmMaterial((FsmObject) source.materialVariables[index]);
      }
      if (source.textureVariables != null)
      {
        this.textureVariables = new FsmTexture[source.textureVariables.Length];
        for (int index = 0; index < source.textureVariables.Length; ++index)
          this.textureVariables[index] = new FsmTexture((FsmObject) source.textureVariables[index]);
      }
      if (source.stringVariables != null)
      {
        this.stringVariables = new FsmString[source.stringVariables.Length];
        for (int index = 0; index < source.stringVariables.Length; ++index)
          this.stringVariables[index] = new FsmString(source.stringVariables[index]);
      }
      if (source.arrayVariables != null)
      {
        this.arrayVariables = new FsmArray[source.arrayVariables.Length];
        for (int index = 0; index < source.arrayVariables.Length; ++index)
          this.arrayVariables[index] = new FsmArray(source.arrayVariables[index]);
      }
      if (source.enumVariables != null)
      {
        this.enumVariables = new FsmEnum[source.enumVariables.Length];
        for (int index = 0; index < source.enumVariables.Length; ++index)
          this.enumVariables[index] = new FsmEnum(source.enumVariables[index]);
      }
      if (source.categories != null)
      {
        this.categories = new string[source.categories.Length];
        for (int index = 0; index < source.categories.Length; ++index)
          this.categories[index] = source.categories[index];
      }
      if (source.CategoryIDs == null)
        return;
      this.CategoryIDs = new int[source.CategoryIDs.Length];
      for (int index = 0; index < source.CategoryIDs.Length; ++index)
        this.CategoryIDs[index] = source.CategoryIDs[index];
    }

    public void Init()
    {
      this._emptyVariables = new List<NamedVariable>();
      this._variableLookup = new Dictionary<string, NamedVariable>();
      foreach (NamedVariable floatVariable in this.FloatVariables)
        this.AddVariableLookup(floatVariable);
      foreach (NamedVariable intVariable in this.IntVariables)
        this.AddVariableLookup(intVariable);
      foreach (NamedVariable boolVariable in this.BoolVariables)
        this.AddVariableLookup(boolVariable);
      foreach (NamedVariable stringVariable in this.StringVariables)
        this.AddVariableLookup(stringVariable);
      foreach (NamedVariable vector2Variable in this.Vector2Variables)
        this.AddVariableLookup(vector2Variable);
      foreach (NamedVariable vector3Variable in this.Vector3Variables)
        this.AddVariableLookup(vector3Variable);
      foreach (NamedVariable rectVariable in this.RectVariables)
        this.AddVariableLookup(rectVariable);
      foreach (NamedVariable quaternionVariable in this.QuaternionVariables)
        this.AddVariableLookup(quaternionVariable);
      foreach (NamedVariable gameObjectVariable in this.GameObjectVariables)
        this.AddVariableLookup(gameObjectVariable);
      foreach (NamedVariable objectVariable in this.ObjectVariables)
        this.AddVariableLookup(objectVariable);
      foreach (NamedVariable materialVariable in this.MaterialVariables)
        this.AddVariableLookup(materialVariable);
      foreach (NamedVariable textureVariable in this.TextureVariables)
        this.AddVariableLookup(textureVariable);
      foreach (NamedVariable colorVariable in this.ColorVariables)
        this.AddVariableLookup(colorVariable);
      foreach (NamedVariable arrayVariable in this.ArrayVariables)
        this.AddVariableLookup(arrayVariable);
      foreach (NamedVariable enumVariable in this.EnumVariables)
        this.AddVariableLookup(enumVariable);
      Dictionary<string, NamedVariable>.ValueCollection values = this.variableLookup.Values;
      this._allVariables = new NamedVariable[values.Count];
      int index = 0;
      foreach (NamedVariable namedVariable in values)
      {
        this._allVariables[index] = namedVariable;
        ++index;
      }
    }

    private void AddVariableLookup(NamedVariable v)
    {
      if (v == null)
        return;
      if (string.IsNullOrEmpty(v.Name))
        this.emptyVariables.Add(v);
      else if (this.variableLookup.ContainsKey(v.Name))
      {
        NamedVariable namedVariable = this.variableLookup[v.Name];
        string str = "variableLookup already contains: " + v.Name;
        if (v.VariableType == namedVariable.VariableType)
          return;
        Debug.LogWarning((object) (str + "\nVariables are of different type: " + (object) v.VariableType + " " + (object) namedVariable.VariableType));
      }
      else
        this.variableLookup.Add(v.Name, v);
    }

    public void Reinitialize()
    {
      this._emptyVariables = (List<NamedVariable>) null;
      this._variableLookup = (Dictionary<string, NamedVariable>) null;
      this._allVariables = (NamedVariable[]) null;
    }

    public int Count => this.allVariables.Length;

    public NamedVariable[] GetAllNamedVariables() => this.allVariables;

    public NamedVariable[] GetAllNamedVariablesSorted()
    {
      List<NamedVariable> namedVariableList = new List<NamedVariable>((IEnumerable<NamedVariable>) this.allVariables);
      namedVariableList.Sort();
      return namedVariableList.ToArray();
    }

    public NamedVariable[] GetNamedVariables(VariableType type)
    {
      if (type == VariableType.Unknown)
        return this.GetAllNamedVariables();
      List<NamedVariable> namedVariableList = new List<NamedVariable>();
      foreach (NamedVariable allVariable in this.allVariables)
      {
        if (allVariable.VariableType == type)
          namedVariableList.Add(allVariable);
      }
      return namedVariableList.ToArray();
    }

    public NamedVariable[] GetNamedVariablesSorted(VariableType type)
    {
      List<NamedVariable> namedVariableList = new List<NamedVariable>((IEnumerable<NamedVariable>) this.GetNamedVariables(type));
      namedVariableList.Sort();
      return namedVariableList.ToArray();
    }

    public List<NamedVariable> GetEmptyVariables() => this.emptyVariables;

    public bool Contains(string variableName) => this.variableLookup.ContainsKey(variableName);

    public bool Contains(NamedVariable variable) => this.variableLookup.ContainsValue(variable);

    public NamedVariable[] GetNames(System.Type ofType) => this.GetNamedVariables(FsmVar.GetVariableType(ofType));

    public int GetVariableIndex(string variableName)
    {
      for (int index = 0; index < this.allVariables.Length; ++index)
      {
        if (this.allVariables[index].Name == variableName)
          return index;
      }
      return -1;
    }

    public static bool AreCompatible(FsmVariables vars1, FsmVariables vars2)
    {
      if (vars1 == null || vars2 == null)
        return false;
      NamedVariable[] allVariables1 = vars1.allVariables;
      NamedVariable[] allVariables2 = vars2.allVariables;
      if (allVariables1.Length != allVariables2.Length)
        return false;
      for (int index = 0; index < allVariables1.Length; ++index)
      {
        NamedVariable namedVariable1 = allVariables1[index];
        NamedVariable namedVariable2 = allVariables2[index];
        if (namedVariable1.VariableType != namedVariable2.VariableType || namedVariable1.ObjectType != namedVariable2.ObjectType || namedVariable1.Name != namedVariable2.Name)
          return false;
      }
      return true;
    }

    public void OverrideVariableValues(FsmVariables source)
    {
      for (int index1 = 0; index1 < source.FloatVariables.Length; ++index1)
      {
        for (int index2 = 0; index2 < this.FloatVariables.Length; ++index2)
        {
          if (this.floatVariables[index2].ShowInInspector && source.floatVariables[index1].Name == this.floatVariables[index2].Name)
            this.floatVariables[index2].Value = source.floatVariables[index1].Value;
        }
      }
      for (int index1 = 0; index1 < source.IntVariables.Length; ++index1)
      {
        for (int index2 = 0; index2 < this.IntVariables.Length; ++index2)
        {
          if (this.intVariables[index2].ShowInInspector && source.intVariables[index1].Name == this.intVariables[index2].Name)
            this.intVariables[index2].Value = source.intVariables[index1].Value;
        }
      }
      for (int index1 = 0; index1 < source.BoolVariables.Length; ++index1)
      {
        for (int index2 = 0; index2 < this.BoolVariables.Length; ++index2)
        {
          if (this.boolVariables[index2].ShowInInspector && source.boolVariables[index1].Name == this.boolVariables[index2].Name)
            this.boolVariables[index2].Value = source.boolVariables[index1].Value;
        }
      }
      for (int index1 = 0; index1 < source.GameObjectVariables.Length; ++index1)
      {
        for (int index2 = 0; index2 < this.GameObjectVariables.Length; ++index2)
        {
          if (this.gameObjectVariables[index2].ShowInInspector && source.gameObjectVariables[index1].Name == this.gameObjectVariables[index2].Name)
            this.gameObjectVariables[index2].Value = source.gameObjectVariables[index1].Value;
        }
      }
      for (int index1 = 0; index1 < source.ColorVariables.Length; ++index1)
      {
        for (int index2 = 0; index2 < this.ColorVariables.Length; ++index2)
        {
          if (this.colorVariables[index2].ShowInInspector && source.colorVariables[index1].Name == this.colorVariables[index2].Name)
            this.colorVariables[index2].Value = source.colorVariables[index1].Value;
        }
      }
      for (int index1 = 0; index1 < source.Vector2Variables.Length; ++index1)
      {
        for (int index2 = 0; index2 < this.Vector2Variables.Length; ++index2)
        {
          if (this.vector2Variables[index2].ShowInInspector && source.vector2Variables[index1].Name == this.vector2Variables[index2].Name)
            this.vector2Variables[index2].Value = source.vector2Variables[index1].Value;
        }
      }
      for (int index1 = 0; index1 < source.Vector3Variables.Length; ++index1)
      {
        for (int index2 = 0; index2 < this.Vector3Variables.Length; ++index2)
        {
          if (this.vector3Variables[index2].ShowInInspector && source.vector3Variables[index1].Name == this.vector3Variables[index2].Name)
            this.vector3Variables[index2].Value = source.vector3Variables[index1].Value;
        }
      }
      for (int index1 = 0; index1 < source.RectVariables.Length; ++index1)
      {
        for (int index2 = 0; index2 < this.RectVariables.Length; ++index2)
        {
          if (this.rectVariables[index2].ShowInInspector && source.rectVariables[index1].Name == this.rectVariables[index2].Name)
            this.rectVariables[index2].Value = source.rectVariables[index1].Value;
        }
      }
      for (int index1 = 0; index1 < source.QuaternionVariables.Length; ++index1)
      {
        for (int index2 = 0; index2 < this.QuaternionVariables.Length; ++index2)
        {
          if (this.quaternionVariables[index2].ShowInInspector && source.quaternionVariables[index1].Name == this.quaternionVariables[index2].Name)
            this.quaternionVariables[index2].Value = source.quaternionVariables[index1].Value;
        }
      }
      for (int index1 = 0; index1 < source.ObjectVariables.Length; ++index1)
      {
        for (int index2 = 0; index2 < this.ObjectVariables.Length; ++index2)
        {
          if (this.objectVariables[index2].ShowInInspector && source.objectVariables[index1].Name == this.objectVariables[index2].Name)
            this.objectVariables[index2].Value = source.objectVariables[index1].Value;
        }
      }
      for (int index1 = 0; index1 < source.MaterialVariables.Length; ++index1)
      {
        for (int index2 = 0; index2 < this.MaterialVariables.Length; ++index2)
        {
          if (this.materialVariables[index2].ShowInInspector && source.materialVariables[index1].Name == this.materialVariables[index2].Name)
            this.materialVariables[index2].Value = source.materialVariables[index1].Value;
        }
      }
      for (int index1 = 0; index1 < source.TextureVariables.Length; ++index1)
      {
        for (int index2 = 0; index2 < this.TextureVariables.Length; ++index2)
        {
          if (this.textureVariables[index2].ShowInInspector && source.textureVariables[index1].Name == this.textureVariables[index2].Name)
            this.textureVariables[index2].Value = source.textureVariables[index1].Value;
        }
      }
      for (int index1 = 0; index1 < source.StringVariables.Length; ++index1)
      {
        for (int index2 = 0; index2 < this.StringVariables.Length; ++index2)
        {
          if (this.stringVariables[index2].ShowInInspector && source.stringVariables[index1].Name == this.stringVariables[index2].Name)
            this.stringVariables[index2].Value = source.stringVariables[index1].Value;
        }
      }
      for (int index1 = 0; index1 < source.ArrayVariables.Length; ++index1)
      {
        for (int index2 = 0; index2 < this.ArrayVariables.Length; ++index2)
        {
          if (this.arrayVariables[index2].ShowInInspector && source.arrayVariables[index1].Name == this.arrayVariables[index2].Name)
            this.arrayVariables[index2].CopyValues(source.arrayVariables[index1]);
        }
      }
      for (int index1 = 0; index1 < source.EnumVariables.Length; ++index1)
      {
        for (int index2 = 0; index2 < this.EnumVariables.Length; ++index2)
        {
          if (this.enumVariables[index2].ShowInInspector && source.enumVariables[index1].Name == this.enumVariables[index2].Name)
            this.enumVariables[index2].Value = source.enumVariables[index1].Value;
        }
      }
    }

    public void ApplyVariableValues(FsmVariables source)
    {
      if (source == null)
        return;
      for (int index = 0; index < source.FloatVariables.Length; ++index)
        this.floatVariables[index].Value = source.floatVariables[index].Value;
      for (int index = 0; index < source.IntVariables.Length; ++index)
        this.intVariables[index].Value = source.intVariables[index].Value;
      for (int index = 0; index < source.BoolVariables.Length; ++index)
        this.boolVariables[index].Value = source.boolVariables[index].Value;
      for (int index = 0; index < source.GameObjectVariables.Length; ++index)
        this.gameObjectVariables[index].Value = source.gameObjectVariables[index].Value;
      for (int index = 0; index < source.ColorVariables.Length; ++index)
        this.colorVariables[index].Value = source.colorVariables[index].Value;
      for (int index = 0; index < source.Vector2Variables.Length; ++index)
        this.vector2Variables[index].Value = source.vector2Variables[index].Value;
      for (int index = 0; index < source.Vector3Variables.Length; ++index)
        this.vector3Variables[index].Value = source.vector3Variables[index].Value;
      for (int index = 0; index < source.RectVariables.Length; ++index)
        this.rectVariables[index].Value = source.rectVariables[index].Value;
      for (int index = 0; index < source.QuaternionVariables.Length; ++index)
        this.quaternionVariables[index].Value = source.quaternionVariables[index].Value;
      for (int index = 0; index < source.ObjectVariables.Length; ++index)
        this.objectVariables[index].Value = source.objectVariables[index].Value;
      for (int index = 0; index < source.MaterialVariables.Length; ++index)
        this.materialVariables[index].Value = source.materialVariables[index].Value;
      for (int index = 0; index < source.TextureVariables.Length; ++index)
        this.textureVariables[index].Value = source.textureVariables[index].Value;
      for (int index = 0; index < source.StringVariables.Length; ++index)
        this.stringVariables[index].Value = source.stringVariables[index].Value;
      for (int index = 0; index < source.EnumVariables.Length; ++index)
        this.enumVariables[index].Value = source.enumVariables[index].Value;
      for (int index = 0; index < source.ArrayVariables.Length; ++index)
        this.arrayVariables[index].CopyValues(source.arrayVariables[index]);
    }

    public void ApplyVariableValuesCareful(FsmVariables source)
    {
      if (source == null)
        return;
      for (int index = 0; index < source.FloatVariables.Length; ++index)
      {
        FsmFloat fsmFloat = this.FindFsmFloat(source.floatVariables[index].Name);
        if (fsmFloat != null)
          fsmFloat.Value = source.floatVariables[index].Value;
      }
      for (int index = 0; index < source.IntVariables.Length; ++index)
      {
        FsmInt fsmInt = this.FindFsmInt(source.IntVariables[index].Name);
        if (fsmInt != null)
          fsmInt.Value = source.IntVariables[index].Value;
      }
      for (int index = 0; index < source.BoolVariables.Length; ++index)
      {
        FsmBool fsmBool = this.FindFsmBool(source.BoolVariables[index].Name);
        if (fsmBool != null)
          fsmBool.Value = source.BoolVariables[index].Value;
      }
      for (int index = 0; index < source.GameObjectVariables.Length; ++index)
      {
        FsmBool fsmBool = this.FindFsmBool(source.BoolVariables[index].Name);
        if (fsmBool != null)
          fsmBool.Value = source.BoolVariables[index].Value;
      }
      for (int index = 0; index < source.ColorVariables.Length; ++index)
      {
        FsmBool fsmBool = this.FindFsmBool(source.BoolVariables[index].Name);
        if (fsmBool != null)
          fsmBool.Value = source.BoolVariables[index].Value;
      }
      for (int index = 0; index < source.Vector2Variables.Length; ++index)
      {
        FsmBool fsmBool = this.FindFsmBool(source.BoolVariables[index].Name);
        if (fsmBool != null)
          fsmBool.Value = source.BoolVariables[index].Value;
      }
      for (int index = 0; index < source.Vector3Variables.Length; ++index)
      {
        FsmBool fsmBool = this.FindFsmBool(source.BoolVariables[index].Name);
        if (fsmBool != null)
          fsmBool.Value = source.BoolVariables[index].Value;
      }
      for (int index = 0; index < source.RectVariables.Length; ++index)
      {
        FsmRect fsmRect = this.FindFsmRect(source.RectVariables[index].Name);
        if (fsmRect != null)
          fsmRect.Value = source.RectVariables[index].Value;
      }
      for (int index = 0; index < source.QuaternionVariables.Length; ++index)
      {
        FsmQuaternion fsmQuaternion = this.FindFsmQuaternion(source.QuaternionVariables[index].Name);
        if (fsmQuaternion != null)
          fsmQuaternion.Value = source.QuaternionVariables[index].Value;
      }
      for (int index = 0; index < source.ObjectVariables.Length; ++index)
      {
        FsmObject fsmObject = this.FindFsmObject(source.ObjectVariables[index].Name);
        if (fsmObject != null)
          fsmObject.Value = source.ObjectVariables[index].Value;
      }
      for (int index = 0; index < source.MaterialVariables.Length; ++index)
      {
        FsmMaterial fsmMaterial = this.FindFsmMaterial(source.MaterialVariables[index].Name);
        if (fsmMaterial != null)
          fsmMaterial.Value = source.MaterialVariables[index].Value;
      }
      for (int index = 0; index < source.TextureVariables.Length; ++index)
      {
        FsmTexture fsmTexture = this.FindFsmTexture(source.TextureVariables[index].Name);
        if (fsmTexture != null)
          fsmTexture.Value = source.TextureVariables[index].Value;
      }
      for (int index = 0; index < source.StringVariables.Length; ++index)
      {
        FsmString fsmString = this.FindFsmString(source.StringVariables[index].Name);
        if (fsmString != null)
          fsmString.Value = source.StringVariables[index].Value;
      }
      for (int index = 0; index < source.EnumVariables.Length; ++index)
      {
        FsmEnum fsmEnum = this.FindFsmEnum(source.EnumVariables[index].Name);
        if (fsmEnum != null)
          fsmEnum.Value = source.EnumVariables[index].Value;
      }
      for (int index = 0; index < source.ArrayVariables.Length; ++index)
        this.FindFsmArray(source.ArrayVariables[index].Name)?.CopyValues(source.arrayVariables[index]);
    }

    public FsmFloat[] FloatVariables
    {
      get => this.floatVariables ?? Arrays<FsmFloat>.Empty;
      set => this.floatVariables = value;
    }

    public FsmInt[] IntVariables
    {
      get => this.intVariables ?? Arrays<FsmInt>.Empty;
      set => this.intVariables = value;
    }

    public FsmBool[] BoolVariables
    {
      get => this.boolVariables ?? Arrays<FsmBool>.Empty;
      set => this.boolVariables = value;
    }

    public FsmString[] StringVariables
    {
      get => this.stringVariables ?? Arrays<FsmString>.Empty;
      set => this.stringVariables = value;
    }

    public FsmVector2[] Vector2Variables
    {
      get => this.vector2Variables ?? Arrays<FsmVector2>.Empty;
      set => this.vector2Variables = value;
    }

    public FsmVector3[] Vector3Variables
    {
      get => this.vector3Variables ?? Arrays<FsmVector3>.Empty;
      set => this.vector3Variables = value;
    }

    public FsmRect[] RectVariables
    {
      get => this.rectVariables ?? Arrays<FsmRect>.Empty;
      set => this.rectVariables = value;
    }

    public FsmQuaternion[] QuaternionVariables
    {
      get => this.quaternionVariables ?? Arrays<FsmQuaternion>.Empty;
      set => this.quaternionVariables = value;
    }

    public FsmColor[] ColorVariables
    {
      get => this.colorVariables ?? Arrays<FsmColor>.Empty;
      set => this.colorVariables = value;
    }

    public FsmGameObject[] GameObjectVariables
    {
      get => this.gameObjectVariables ?? Arrays<FsmGameObject>.Empty;
      set => this.gameObjectVariables = value;
    }

    public FsmArray[] ArrayVariables
    {
      get => this.arrayVariables ?? Arrays<FsmArray>.Empty;
      set => this.arrayVariables = value;
    }

    public FsmEnum[] EnumVariables
    {
      get => this.enumVariables ?? Arrays<FsmEnum>.Empty;
      set => this.enumVariables = value;
    }

    public FsmObject[] ObjectVariables
    {
      get => this.objectVariables ?? Arrays<FsmObject>.Empty;
      set => this.objectVariables = value;
    }

    public FsmMaterial[] MaterialVariables
    {
      get => this.materialVariables ?? Arrays<FsmMaterial>.Empty;
      set => this.materialVariables = value;
    }

    public FsmTexture[] TextureVariables
    {
      get => this.textureVariables ?? Arrays<FsmTexture>.Empty;
      set => this.textureVariables = value;
    }

    public NamedVariable GetVariable(string name)
    {
      if (string.IsNullOrEmpty(name))
        return (NamedVariable) null;
      NamedVariable variable;
      this.variableLookup.TryGetValue(name, out variable);
      if (variable == null)
        variable = FsmVariables.GlobalVariables.FindVariable(name);
      return variable != null && !PlayMakerGlobals.IsPlaying ? variable.Copy() : variable;
    }

    public NamedVariable GetVariable(VariableType variableType, string name)
    {
      if (string.IsNullOrEmpty(name))
        return (NamedVariable) null;
      NamedVariable variable = this.GetVariable(name);
      if (variable == null)
        return (NamedVariable) null;
      if (variable.VariableType == variableType)
        return variable;
      switch (variableType)
      {
        case VariableType.Unknown:
          return (NamedVariable) null;
        case VariableType.Float:
          FsmFloat fsmFloat = new FsmFloat(name);
          fsmFloat.CastVariable = this.GetVariable(name);
          return (NamedVariable) fsmFloat;
        case VariableType.Int:
          FsmInt fsmInt = new FsmInt(name);
          fsmInt.CastVariable = this.GetVariable(name);
          return (NamedVariable) fsmInt;
        case VariableType.Bool:
          FsmBool fsmBool = new FsmBool(name);
          fsmBool.CastVariable = this.GetVariable(name);
          return (NamedVariable) fsmBool;
        case VariableType.GameObject:
          FsmGameObject fsmGameObject = new FsmGameObject(name);
          fsmGameObject.CastVariable = this.GetVariable(name);
          return (NamedVariable) fsmGameObject;
        case VariableType.String:
          FsmString fsmString = new FsmString(name);
          fsmString.CastVariable = this.GetVariable(name);
          return (NamedVariable) fsmString;
        case VariableType.Vector2:
          FsmVector2 fsmVector2 = new FsmVector2(name);
          fsmVector2.CastVariable = this.GetVariable(name);
          return (NamedVariable) fsmVector2;
        case VariableType.Vector3:
          FsmVector3 fsmVector3 = new FsmVector3(name);
          fsmVector3.CastVariable = this.GetVariable(name);
          return (NamedVariable) fsmVector3;
        case VariableType.Color:
          FsmColor fsmColor = new FsmColor(name);
          fsmColor.CastVariable = this.GetVariable(name);
          return (NamedVariable) fsmColor;
        case VariableType.Rect:
          FsmRect fsmRect = new FsmRect(name);
          fsmRect.CastVariable = this.GetVariable(name);
          return (NamedVariable) fsmRect;
        case VariableType.Material:
          FsmMaterial fsmMaterial = new FsmMaterial(name);
          fsmMaterial.CastVariable = this.GetVariable(name);
          return (NamedVariable) fsmMaterial;
        case VariableType.Texture:
          FsmTexture fsmTexture = new FsmTexture(name);
          fsmTexture.CastVariable = this.GetVariable(name);
          return (NamedVariable) fsmTexture;
        case VariableType.Quaternion:
          FsmQuaternion fsmQuaternion = new FsmQuaternion(name);
          fsmQuaternion.CastVariable = this.GetVariable(name);
          return (NamedVariable) fsmQuaternion;
        case VariableType.Object:
          FsmObject fsmObject = new FsmObject(name);
          fsmObject.CastVariable = this.GetVariable(name);
          return (NamedVariable) fsmObject;
        case VariableType.Array:
          FsmArray fsmArray = new FsmArray(name);
          fsmArray.CastVariable = this.GetVariable(name);
          return (NamedVariable) fsmArray;
        case VariableType.Enum:
          FsmEnum fsmEnum = new FsmEnum(name);
          fsmEnum.CastVariable = this.GetVariable(name);
          return (NamedVariable) fsmEnum;
        default:
          throw new ArgumentOutOfRangeException(nameof (variableType), (object) variableType, (string) null);
      }
    }

    public FsmFloat GetFsmFloat(string name)
    {
      if (this.GetVariable(name) is FsmFloat variable)
        return variable;
      FsmFloat fsmFloat = new FsmFloat(name);
      fsmFloat.CastVariable = this.GetVariable(name);
      return fsmFloat;
    }

    public FsmObject GetFsmObject(string name)
    {
      if (this.GetVariable(name) is FsmObject variable)
        return variable;
      FsmObject fsmObject = new FsmObject(name);
      fsmObject.CastVariable = this.GetVariable(name);
      return fsmObject;
    }

    public FsmMaterial GetFsmMaterial(string name)
    {
      if (this.GetVariable(name) is FsmMaterial variable)
        return variable;
      FsmMaterial fsmMaterial = new FsmMaterial(name);
      fsmMaterial.CastVariable = this.GetVariable(name);
      return fsmMaterial;
    }

    public FsmTexture GetFsmTexture(string name)
    {
      if (this.GetVariable(name) is FsmTexture variable)
        return variable;
      FsmTexture fsmTexture = new FsmTexture(name);
      fsmTexture.CastVariable = this.GetVariable(name);
      return fsmTexture;
    }

    public FsmInt GetFsmInt(string name)
    {
      if (this.GetVariable(name) is FsmInt variable)
        return variable;
      FsmInt fsmInt = new FsmInt(name);
      fsmInt.CastVariable = this.GetVariable(name);
      return fsmInt;
    }

    public FsmBool GetFsmBool(string name)
    {
      if (this.GetVariable(name) is FsmBool variable)
        return variable;
      FsmBool fsmBool = new FsmBool(name);
      fsmBool.CastVariable = this.GetVariable(name);
      return fsmBool;
    }

    public FsmString GetFsmString(string name)
    {
      if (this.GetVariable(name) is FsmString variable)
        return variable;
      FsmString fsmString = new FsmString(name);
      fsmString.CastVariable = this.GetVariable(name);
      return fsmString;
    }

    public FsmVector2 GetFsmVector2(string name) => this.GetVariable(name) is FsmVector2 variable ? variable : new FsmVector2(name);

    public FsmVector3 GetFsmVector3(string name)
    {
      if (this.GetVariable(name) is FsmVector3 variable)
        return variable;
      FsmVector3 fsmVector3 = new FsmVector3(name);
      fsmVector3.CastVariable = this.GetVariable(name);
      return fsmVector3;
    }

    public FsmRect GetFsmRect(string name) => this.GetVariable(name) is FsmRect variable ? variable : new FsmRect(name);

    public FsmQuaternion GetFsmQuaternion(string name) => this.GetVariable(name) is FsmQuaternion variable ? variable : new FsmQuaternion(name);

    public FsmColor GetFsmColor(string name) => this.GetVariable(name) is FsmColor variable ? variable : new FsmColor(name);

    public FsmGameObject GetFsmGameObject(string name)
    {
      if (this.GetVariable(name) is FsmGameObject variable)
        return variable;
      FsmGameObject fsmGameObject = new FsmGameObject(name);
      fsmGameObject.CastVariable = this.GetVariable(name);
      return fsmGameObject;
    }

    public FsmArray GetFsmArray(string name) => this.GetVariable(name) is FsmArray variable ? variable : new FsmArray(name);

    public FsmEnum GetFsmEnum(string name)
    {
      if (this.GetVariable(name) is FsmEnum variable)
        return variable;
      FsmEnum fsmEnum = new FsmEnum(name);
      fsmEnum.CastVariable = this.GetVariable(name);
      return fsmEnum;
    }

    public NamedVariable FindVariable(string name)
    {
      NamedVariable namedVariable;
      this.variableLookup.TryGetValue(name, out namedVariable);
      return namedVariable;
    }

    public NamedVariable LoadGlobalVariable(string name)
    {
      NamedVariable namedVariable;
      this.variableLookup.TryGetValue(name, out namedVariable);
      return namedVariable != null && !PlayMakerGlobals.IsPlaying ? namedVariable.Copy() : namedVariable;
    }

    public NamedVariable FindVariable(VariableType type, string name)
    {
      NamedVariable namedVariable;
      if (!this.variableLookup.TryGetValue(name, out namedVariable))
        return (NamedVariable) null;
      return namedVariable.VariableType != type ? (NamedVariable) null : namedVariable;
    }

    public FsmFloat FindFsmFloat(string name) => this.FindVariable(VariableType.Float, name) as FsmFloat;

    public FsmObject FindFsmObject(string name) => this.FindVariable(VariableType.Object, name) as FsmObject;

    public FsmMaterial FindFsmMaterial(string name) => this.FindVariable(VariableType.Material, name) as FsmMaterial;

    public FsmTexture FindFsmTexture(string name) => this.FindVariable(VariableType.Texture, name) as FsmTexture;

    public FsmInt FindFsmInt(string name) => this.FindVariable(VariableType.Int, name) as FsmInt;

    public FsmBool FindFsmBool(string name) => this.FindVariable(VariableType.Bool, name) as FsmBool;

    public FsmString FindFsmString(string name) => this.FindVariable(VariableType.String, name) as FsmString;

    public FsmVector2 FindFsmVector2(string name) => this.FindVariable(VariableType.Vector2, name) as FsmVector2;

    public FsmVector3 FindFsmVector3(string name) => this.FindVariable(VariableType.Vector3, name) as FsmVector3;

    public FsmRect FindFsmRect(string name) => this.FindVariable(VariableType.Rect, name) as FsmRect;

    public FsmQuaternion FindFsmQuaternion(string name) => this.FindVariable(VariableType.Quaternion, name) as FsmQuaternion;

    public FsmColor FindFsmColor(string name) => this.FindVariable(VariableType.Color, name) as FsmColor;

    public FsmGameObject FindFsmGameObject(string name) => this.FindVariable(VariableType.GameObject, name) as FsmGameObject;

    public FsmEnum FindFsmEnum(string name) => this.FindVariable(VariableType.Enum, name) as FsmEnum;

    public FsmArray FindFsmArray(string name) => this.FindVariable(VariableType.Array, name) as FsmArray;
  }
}

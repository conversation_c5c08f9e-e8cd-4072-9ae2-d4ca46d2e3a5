// Decompiled with JetBrains decompiler
// Type: HutongGames.LogHelper
// Assembly: PlayMaker, Version=1.6.0.0, Culture=neutral, PublicKeyToken=null
// MVID: 5B31C0E3-F019-417B-9B9A-403AD5BD3AAE
// Assembly location: E:\UnityProjects\My project (4)\Assets\Plugins\PlayMaker\PlayMaker.dll

using System.Diagnostics;

namespace HutongGames
{
  public static class LogHelper
  {
    [Conditional("DEBUG_LOG")]
    public static void LogWarning(object prefix, object message) => UnityEngine.Debug.LogWarning((object) (prefix.ToString() + ": " + message));

    [Conditional("DEBUG_LOG")]
    public static void Log(object message, LogColor logColor = LogColor.None) => UnityEngine.Debug.Log(LogHelper.FormatLog(message, logColor));

    [Conditional("DEBUG_LOG")]
    public static void Log(object prefix, object message, LogColor logColor = LogColor.None) => UnityEngine.Debug.Log((object) (prefix.ToString() + ": " + LogHelper.FormatLog(message, logColor)));

    [Conditional("DEBUG_LOG")]
    public static void Log(object prefix, object message, object postfix, LogColor logColor = LogColor.None) => UnityEngine.Debug.Log((object) (prefix.ToString() + ": " + LogHelper.FormatLog(message, logColor) + " \t" + postfix));

    private static object FormatLog(object message, LogColor logColor)
    {
      switch (logColor)
      {
        case LogColor.Green:
          return (object) ("<color=green>" + message + "</color>");
        case LogColor.Yellow:
          return (object) ("<color=yellow>" + message + "</color>");
        case LogColor.Red:
          return (object) ("<color=red>" + message + "</color>");
        default:
          return message;
      }
    }
  }
}

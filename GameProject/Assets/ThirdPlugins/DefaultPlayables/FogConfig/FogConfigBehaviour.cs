using System;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;

[Serializable]
public class FogConfigBehaviour : PlayableBehaviour
{
    public int DistanceOffset = 0;
    public int HeightOffset = 0;
    public bool FogEnable = true;
    public float NoiseAlpha = 1f;
    public int HeightMax = 30;
    public int HeightMin = 19;
    public int DistanceMax = 54;
    public int DistanceMin = 26;
}

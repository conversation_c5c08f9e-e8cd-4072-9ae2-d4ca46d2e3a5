Shader "Hidden/Overdraw-Transparent"
{
	SubShader
	{
		Tags { "RenderType" = "Transparent" "Queue" = "Transparent" "RenderPipeline" = "UniversalPipeline"}

		Pass
		{
			ZWrite Off
			ZTest Always
			Blend One One
			Cull Off

			HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Color.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            
            struct appdata
            {
            	float4 vertex : POSITION;
            };
            
            struct v2f
            {
            	float4 vertex : SV_POSITION;
            };
            
            v2f vert(appdata v)
            {
            	v2f o;
            	o.vertex = TransformObjectToHClip(v.vertex.xyz);
            	return o;
            }
            
            half4 frag(v2f i) : SV_Target
            {
            	return half4(0.1, 0.04, 0.02, 1);
            }
            
            ENDHLSL
		}
	}
}
